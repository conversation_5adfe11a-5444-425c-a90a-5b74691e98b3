import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import apiService from '../services/api';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Vérifier l'authentification au démarrage
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const userData = await AsyncStorage.getItem('userData');
      if (userData) {
        const parsedUser = JSON.parse(userData);
        setUser(parsedUser);
        setIsAuthenticated(true);
        
        // Vérifier si le token est toujours valide
        try {
          await apiService.getUserProfile();
        } catch (error) {
          console.log('Token validation failed:', error.message);
          // Token invalide, déconnecter l'utilisateur
          await logout();
        }
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
    } finally {
      setLoading(false);
    }
  };

  const login = async (credentials) => {
    try {
      setLoading(true);
      
      console.log('Attempting login with:', credentials.email);
      
      const response = await apiService.login(credentials);
      console.log('Login response:', response);
      
      // Stocker le token si fourni dans la réponse
      let userData = {
        id: response.user?.id,
        email: credentials.email,
        isAuthenticated: true,
        loginDate: new Date().toISOString(),
      };

      // Si l'API retourne un token, le stocker
      if (response.token) {
        userData.token = response.token;
        console.log('Token stored from response');
      }

      // Vérifier le token avec l'endpoint de vérification
      try {
        console.log('Verifying token...');
        const verifyResponse = await apiService.verifyToken();
        console.log('Token verification response:', verifyResponse);
        
        if (verifyResponse.valid && verifyResponse.user) {
          userData = {
            ...userData,
            ...verifyResponse.user,
          };
          console.log('User data updated from token verification');
        }
      } catch (verifyError) {
        console.error('Token verification failed:', verifyError);
        
        // Essayer de récupérer le profil utilisateur
        try {
          console.log('Trying to get user profile...');
          const profileResponse = await apiService.getUserProfile();
          console.log('Profile response:', profileResponse);
          
          userData = {
            ...userData,
            ...profileResponse.user,
          };
        } catch (profileError) {
          console.error('Profile fetch failed:', profileError);
          // Continuer avec les données de base si le profil échoue
        }
      }

      console.log('Final user data to store:', userData);
      await AsyncStorage.setItem('userData', JSON.stringify(userData));
      setUser(userData);
      setIsAuthenticated(true);
      
      return response;
    } catch (error) {
      console.error('Login error:', error);
      
      // Si erreur de backend, proposer le mode de test
      if (error.message.includes('Token invalide') || error.message.includes('Network request failed')) {
        throw new Error('Problème de backend. Vérifiez que votre serveur est démarré sur le port 5000.');
      }
      
      // Gestion spécifique des erreurs de connexion
      if (error.message.includes('Token error')) {
        throw new Error('Erreur d\'authentification. Vérifiez vos identifiants.');
      }
      
      if (error.message.includes('Invalid credentials')) {
        throw new Error('Email ou mot de passe incorrect.');
      }
      
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const register = async (userData) => {
    try {
      setLoading(true);
      const response = await apiService.register(userData);
      
      // Après inscription, connecter automatiquement l'utilisateur
      await login({
        email: userData.email,
        password: userData.password,
      });
      
      return response;
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      await apiService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      await AsyncStorage.removeItem('userData');
      await AsyncStorage.removeItem('userVotes');
      setUser(null);
      setIsAuthenticated(false);
    }
  };

  const updateUser = (newUserData) => {
    setUser(prev => ({ ...prev, ...newUserData }));
  };

  const value = {
    user,
    loading,
    isAuthenticated,
    login,
    register,
    logout,
    updateUser,
    checkAuthStatus,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}; 