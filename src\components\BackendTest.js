import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import apiService from '../services/api';

const BackendTest = () => {
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState([]);

  const addResult = (test, success, message) => {
    setResults(prev => [...prev, { test, success, message, timestamp: new Date() }]);
  };

  const runBackendTests = async () => {
    setTesting(true);
    setResults([]);

    try {
      // Test 1: Santé du serveur
      addResult('Santé du serveur', 'testing', 'Vérification...');
      try {
        const health = await apiService.checkHealth();
        addResult('Santé du serveur', 'success', `OK - ${JSON.stringify(health)}`);
      } catch (error) {
        addResult('Santé du serveur', 'error', error.message);
      }

      // Test 2: Connexion
      addResult('Test de connexion', 'testing', 'Tentative de connexion...');
      try {
        const loginResponse = await apiService.login({
          email: '<EMAIL>',
          password: 'Test123!'
        });
        addResult('Test de connexion', 'success', `Connecté - ${JSON.stringify(loginResponse)}`);
      } catch (error) {
        addResult('Test de connexion', 'error', error.message);
        setTesting(false);
        return; // Arrêter si la connexion échoue
      }

      // Test 3: Profil utilisateur
      addResult('Profil utilisateur', 'testing', 'Récupération du profil...');
      try {
        const profile = await apiService.getUserProfile();
        addResult('Profil utilisateur', 'success', `Profil récupéré - ${JSON.stringify(profile)}`);
      } catch (error) {
        addResult('Profil utilisateur', 'error', error.message);
      }

      // Test 4: Catégories
      addResult('Récupération catégories', 'testing', 'Chargement des catégories...');
      try {
        const categories = await apiService.getCategories();
        addResult('Récupération catégories', 'success', `${categories.length} catégories trouvées`);
      } catch (error) {
        addResult('Récupération catégories', 'error', error.message);
      }

      // Test 5: Stars
      addResult('Récupération stars', 'testing', 'Chargement des stars...');
      try {
        const stars = await apiService.getStars();
        addResult('Récupération stars', 'success', `${stars.length} stars trouvées`);
      } catch (error) {
        addResult('Récupération stars', 'error', error.message);
      }

    } catch (error) {
      addResult('Test général', 'error', error.message);
    } finally {
      setTesting(false);
    }
  };

  const getResultIcon = (success) => {
    switch (success) {
      case 'success':
        return <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />;
      case 'error':
        return <Ionicons name="close-circle" size={20} color="#f44336" />;
      case 'testing':
        return <Ionicons name="sync" size={20} color="#2196F3" />;
      default:
        return <Ionicons name="help-circle" size={20} color="#FF9800" />;
    }
  };

  const getResultColor = (success) => {
    switch (success) {
      case 'success':
        return '#4CAF50';
      case 'error':
        return '#f44336';
      case 'testing':
        return '#2196F3';
      default:
        return '#FF9800';
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Test Backend</Text>
      
      <TouchableOpacity
        style={[styles.testButton, testing && styles.testButtonDisabled]}
        onPress={runBackendTests}
        disabled={testing}
      >
        <Ionicons name="bug" size={20} color="#fff" />
        <Text style={styles.testButtonText}>
          {testing ? 'Tests en cours...' : 'Lancer les tests backend'}
        </Text>
      </TouchableOpacity>

      <ScrollView style={styles.resultsContainer}>
        {results.map((result, index) => (
          <View key={index} style={styles.resultItem}>
            <View style={styles.resultHeader}>
              {getResultIcon(result.success)}
              <Text style={[styles.resultTest, { color: getResultColor(result.success) }]}>
                {result.test}
              </Text>
            </View>
            <Text style={styles.resultMessage}>{result.message}</Text>
            <Text style={styles.resultTime}>
              {result.timestamp.toLocaleTimeString()}
            </Text>
          </View>
        ))}
      </ScrollView>

      {results.length > 0 && (
        <View style={styles.summary}>
          <Text style={styles.summaryText}>
            Tests terminés : {results.filter(r => r.success !== 'testing').length}/{results.length}
          </Text>
          <Text style={styles.summaryText}>
            Succès : {results.filter(r => r.success === 'success').length}
          </Text>
          <Text style={styles.summaryText}>
            Erreurs : {results.filter(r => r.success === 'error').length}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#1a1a2e',
    padding: 20,
    borderRadius: 12,
    margin: 20,
  },
  title: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  testButton: {
    backgroundColor: '#2196F3',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginBottom: 15,
  },
  testButtonDisabled: {
    backgroundColor: '#666',
  },
  testButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  resultsContainer: {
    maxHeight: 300,
  },
  resultItem: {
    backgroundColor: '#16213e',
    padding: 10,
    borderRadius: 8,
    marginBottom: 8,
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  resultTest: {
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  resultMessage: {
    color: '#b0b0b0',
    fontSize: 12,
    marginLeft: 28,
  },
  resultTime: {
    color: '#666',
    fontSize: 10,
    marginLeft: 28,
    marginTop: 2,
  },
  summary: {
    marginTop: 15,
    padding: 10,
    backgroundColor: '#16213e',
    borderRadius: 8,
  },
  summaryText: {
    color: '#b0b0b0',
    fontSize: 12,
    marginBottom: 2,
  },
});

export default BackendTest; 