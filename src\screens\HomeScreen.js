import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import dataService from '../services/dataService';
import ConnectionTest from '../components/ConnectionTest';
import BackendTest from '../components/BackendTest';
import TokenDebug from '../components/TokenDebug';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import GradientView from '../components/ui/GradientView';
import theme from '../theme/theme';

const { width, height } = Dimensions.get('window');

const HomeScreen = ({ navigation }) => {
  const { isAuthenticated, user } = useAuth();
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      setLoading(true);
      const formattedCategories = await dataService.getFormattedCategories();
      setCategories(formattedCategories);
    } catch (error) {
      console.error('Error loading categories:', error);
      Alert.alert('Erreur', 'Impossible de charger les catégories. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };

  const handleStartVoting = () => {
    if (isAuthenticated) {
      navigation.navigate('Dashboard');
    } else {
      navigation.navigate('Auth');
    }
  };

  return (
    <GradientView gradient="premium" style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Header avec logo */}
        <Card variant="glass" style={styles.header}>
          <View style={styles.logoContainer}>
            <Ionicons name="trophy" size={80} color={theme.colors.gold} />
            <Text style={styles.logoText}>AWARDS 2025</Text>
            <Text style={styles.logoSubtitle}>Votez pour vos stars préférées</Text>
          </View>
        </Card>

        {/* Section principale */}
        <View style={styles.mainSection}>
          <Card variant="elevated" style={styles.heroCard}>
            <Text style={styles.title}>
              Votez pour vos stars préférées !
            </Text>
            
            <Text style={styles.subtitle}>
              Participez au plus grand événement de l'année et gagnez des prix exceptionnels
            </Text>
          </Card>

          {/* Catégories preview */}
          <Card variant="accent" style={styles.categoriesPreview}>
            <Text style={styles.categoriesTitle}>
              Catégories disponibles ({categories.length}) :
            </Text>
            {loading ? (
              <Text style={styles.loadingText}>Chargement des catégories...</Text>
            ) : (
              <View style={styles.categoryList}>
                {categories.slice(0, 4).map((category, index) => (
                  <View key={category.id} style={styles.categoryItem}>
                    <View style={[styles.categoryIcon, { backgroundColor: category.color }]}>
                      <Ionicons name={category.icon} size={20} color="#fff" />
                    </View>
                    <Text style={styles.categoryText}>{category.name}</Text>
                  </View>
                ))}
                {categories.length === 0 && (
                  <Text style={styles.noCategoriesText}>Aucune catégorie disponible</Text>
                )}
              </View>
            )}
          </Card>

          {/* Prix à gagner */}
          <Card variant="primary" style={styles.prizesSection}>
            <Text style={styles.prizesTitle}>🎁 Prix à gagner :</Text>
            <View style={styles.prizesList}>
              <Text style={styles.prizeItem}>• Voyage de rêve pour 2 personnes</Text>
              <Text style={styles.prizeItem}>• Smartphone dernière génération</Text>
              <Text style={styles.prizeItem}>• Bons d'achat de 500€</Text>
              <Text style={styles.prizeItem}>• Et bien d'autres surprises...</Text>
            </View>
          </Card>

     

          {/* Tests de développement */}
          {__DEV__ && (
            <>
              <ConnectionTest />
              <BackendTest />
              <TokenDebug />
            </>
          )}
        </View>

        {/* Bouton d'action */}
        <View style={styles.actionSection}>
          <Button
            title={isAuthenticated ? 'Commencer à voter' : "S'inscrire pour voter"}
            onPress={handleStartVoting}
            variant="primary"
            size="large"
            icon={isAuthenticated ? "checkmark-circle" : "person-add"}
            style={styles.actionButton}
          />

          <Text style={styles.infoText}>
            {isAuthenticated 
              ? `Connecté en tant que ${user?.email}`
              : 'Inscription gratuite • Vote sécurisé • Tirage au sort équitable'
            }
          </Text>
        </View>
      </ScrollView>
      </SafeAreaView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 30,
  },
  header: {
    alignItems: 'center',
    paddingVertical: 30,
    marginBottom: 20,
  },
  logoContainer: {
    alignItems: 'center',
  },
  logoText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: theme.colors.gold,
    marginTop: 10,
    letterSpacing: 2,
  },
  logoSubtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 5,
    textAlign: 'center',
  },
  mainSection: {
    padding: 20,
  },
  heroCard: {
    marginBottom: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    textAlign: 'center',
    marginBottom: 15,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  categoriesPreview: {
    marginBottom: 20,
  },
  categoriesTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    marginBottom: 15,
  },
  categoryList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '48%',
    marginBottom: 15,
  },
  categoryIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  categoryText: {
    color: theme.colors.textPrimary,
    fontSize: 14,
    fontWeight: '500',
  },
  loadingText: {
    color: theme.colors.textTertiary,
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  noCategoriesText: {
    color: theme.colors.textTertiary,
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  prizesSection: {
    marginBottom: 20,
  },
  prizesTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    marginBottom: 15,
  },
  prizesList: {
    paddingLeft: 10,
  },
  prizeItem: {
    color: theme.colors.textSecondary,
    fontSize: 14,
    lineHeight: 22,
    marginBottom: 5,
  },
  actionSection: {
    padding: 20,
    alignItems: 'center',
  },
  actionButton: {
    width: width * 0.8,
  },
  registerButton: {
    backgroundColor: '#ff6b6b',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 25,
    width: width * 0.8,
    elevation: 5,
    shadowColor: '#ff6b6b',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  registerButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 10,
  },
  infoText: {
    color: theme.colors.textTertiary,
    fontSize: 12,
    textAlign: 'center',
    marginTop: 15,
    lineHeight: 18,
  },
});

export default HomeScreen;
