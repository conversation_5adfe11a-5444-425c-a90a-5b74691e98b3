import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Image,
  ImageBackground ,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import Button from '../components/ui/Button';
import GradientView from '../components/ui/GradientView';
import CountdownTimer from '../components/ui/CountdownTimer';
import CelebrityCard from '../components/ui/CelebrityCard';
import CategoryIcon from '../components/ui/CategoryIcon';
import PrizeCard from '../components/ui/PrizeCard';
import theme from '../theme/theme';

const HomeScreen = ({ navigation }) => {
  const { isAuthenticated } = useAuth();

  // Mock data for the new design
  const celebrities = [
    {
      id: 1,
      name: '<PERSON>',
      category: 'Actor',
      image: require('../../assets/celebrities/liam-carter.png'),
      votes: 1234
    },
    {
      id: 2,
      name: '<PERSON>',
      category: 'Singer',
      image: require('../../assets/celebrities/isabella-rossi.png'),
      votes: 2156
    },
    {
      id: 3,
      name: '<PERSON> <PERSON>',
      category: 'Director',
      image: require('../../assets/celebrities/noah-thompson.png'),
      votes: 987
    }
  ];

  const prizes = [
    {
      id: 1,
      title: 'Tablette',
      description: 'iPad Pro dernière génération',
      image: require('../../assets/prizes/tablet.png')
    },
    {
      id: 2,
      title: 'Samsung',
      description: 'Galaxy S24 Ultra',
      image: require('../../assets/prizes/samsung.png')
    },
    {
      id: 3,
      title: 'Smart TV',
      description: 'TV 4K 65 pouces',
      image: require('../../assets/prizes/smart-tv.png')
    }
  ];

  const categoryIcons = [
    { id: 1, icon: 'film', label: 'Cinéma' },
    { id: 2, icon: 'musical-notes', label: 'Musique' },
    { id: 3, icon: 'tv', label: 'TV' },
    { id: 4, icon: 'game-controller', label: 'Gaming' },
    { id: 5, icon: 'book', label: 'Littérature' },
    { id: 6, icon: 'basketball', label: 'Sport' }
  ];



  const handleStartVoting = () => {
    if (isAuthenticated) {
      navigation.navigate('Dashboard');
    } else {
      navigation.navigate('Auth');
    }
  };

  const handleCelebrityPress = (celebrity) => {
    Alert.alert('Vote', `Voter pour ${celebrity.name}?`);
  };

  const handleCategoryPress = () => {
    navigation.navigate('Dashboard');
  };

  const handlePrizePress = (prize) => {
    Alert.alert('Prix', `Détails du prix: ${prize.title}`);
  };

  return (
    <GradientView gradient="primary" style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerLeft}>
              <Text style={styles.pageTitle}>Accueil</Text>
            </View>
            <TouchableOpacity style={styles.profileButton}>
              <Ionicons name="person-circle" size={32} color={theme.colors.accent} />
            </TouchableOpacity>
          </View>
          <View style={styles.headerLeft}>
              <Text style={styles.welcomeText}>Bienvenue sur Leaders Awards</Text>
            </View>

          {/* Hero Section */}
<View style={styles.heroWrapper}>
  <ImageBackground
    source={require('../../assets/homeImage.png')}
    style={styles.heroSection}
    imageStyle={styles.heroImage}
  >
    <Text style={styles.heroTitle}>
      Vote for your{'\n'}favorite celebrity
    </Text>
    <Text style={styles.heroSubtitle}>
      Cast your vote now and help determine the{'\n'}
      winners of this year's most prestigious Leaders Award
    </Text>
    <Button
      title="Commencer le vote"
      onPress={handleStartVoting}
      size="small"
      textStyle={{ fontSize: theme.typography.fontSize.lg ,padding: theme.spacing.xs }}

      variant="primary"
      style={styles.voteButton}
    />
  </ImageBackground>
</View>

          {/* Countdown */}
          <View style={styles.countdownSection}>
            <Text style={styles.sectionTitle}>Countdown</Text>
            <CountdownTimer
              targetDate="2025-12-31T23:59:59"
              style={styles.countdown}
            />
          </View>

          {/* Categories */}
          <View style={styles.categoriesSection}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Catégories</Text>
              <TouchableOpacity>
                <Text style={styles.viewAllText}>Voir Tout</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.categoriesGrid}>
              {categoryIcons && categoryIcons.map((category) => (
                <CategoryIcon
                  key={category.id}
                  icon={category.icon}
                  label={category.label}
                  onPress={() => handleCategoryPress()}
                />
              ))}
            </View>
          </View>

          {/* Recommendations */}
          <View style={styles.recommendationsSection}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Recommandé pour vous</Text>
              <TouchableOpacity>
                <Text style={styles.viewAllText}>Voir Tout</Text>
              </TouchableOpacity>
            </View>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.horizontalScroll}>
              {celebrities && celebrities.map((celebrity) => (
                <CelebrityCard
                  key={celebrity.id}
                  name={celebrity.name}
                  category={celebrity.category}
                  image={celebrity.image}
                  votes={celebrity.votes}
                  onPress={() => handleCelebrityPress(celebrity)}
                />
              ))}
            </ScrollView>
          </View>

          {/* Event Section */}
          <View style={styles.eventSection}>
            <Text style={styles.eventTitle}>Notre Événement</Text>
            <Text style={styles.eventDescription}>
              Découvrez les moments forts et les{'\n'}
              événements à venir de nos Leaders Awards
            </Text>
          </View>

          {/* Prizes */}
          <View style={styles.prizesSection}>
            <Text style={styles.sectionTitle}>Les Prix à gagner</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.horizontalScroll}>
              {prizes && prizes.map((prize) => (
                <PrizeCard
                  key={prize.id}
                  title={prize.title}
                  description={prize.description}
                  image={prize.image}
                  onPress={() => handlePrizePress(prize)}
                />
              ))}
            </ScrollView>
          </View>
        </ScrollView>
      </SafeAreaView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 100,
    
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 20,
  },
  headerLeft: {
    flex: 1,
  },
  pageTitle: {
    fontSize: 20,
    color: theme.colors.textPrimary,
    marginBottom: 4,
    fontWeight: 'bold',
    alignSelf: 'center',
    paddingStart: 40,
  },
  welcomeText: {
    fontSize: 24,
    color: theme.colors.textPrimary,
    marginBottom: 4,
    fontWeight: 'bold',
    paddingHorizontal: 20,


  },
  appTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
  },
  profileButton: {
   //padding: 8,
  },
  heroWrapper: {
  //  marginHorizontal: 20,
  ///  marginBottom: 30,
  //  borderRadius: 20,
    overflow: 'hidden', // ensures the image respects rounded corners
  },
  heroSection: {
    paddingTop: 220,
    paddingHorizontal: 20,
    paddingVertical: 40,
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
  heroImage: {
    resizeMode: 'cover', // fills the whole section
    //opacity: 0.6,        // makes the image darker for better text contrast
  },
  heroTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    textAlign: 'left',
    marginBottom: 16,
    lineHeight: 38,
  },
  heroSubtitle: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'left',
    marginBottom: 30,
    lineHeight: 22,
  },
  voteButton: {
    paddingHorizontal: 80,
    borderRadius: 50,

  },
  countdownSection: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  countdown: {
    marginTop: 15,
  },
  categoriesSection: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
  },
  viewAllText: {
    fontSize: 14,
    color: theme.colors.accent,
    fontWeight: '600',
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  recommendationsSection: {
    marginBottom: 30,
  },
  horizontalScroll: {
    paddingLeft: 20,
  },
  eventSection: {
    paddingHorizontal: 20,
    paddingVertical: 30,
    alignItems: 'center',
    marginBottom: 30,
  },
  eventTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    textAlign: 'center',
    marginBottom: 12,
  },
  eventDescription: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  prizesSection: {
    marginBottom: 30,
  },
});

export default HomeScreen;
