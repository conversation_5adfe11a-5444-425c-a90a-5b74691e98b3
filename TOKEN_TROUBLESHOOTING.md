# Guide de dépannage - Erreurs de Token

## 🔐 **Diagnostic des erreurs "Token error"**

### **1. Comprendre l'erreur**

L'erreur "Token error" indique un problème avec l'authentification JWT. Cela peut être dû à :

- **Token manquant** : L'utilisateur n'est pas connecté
- **Token expiré** : Le token JWT a dépassé sa durée de validité
- **Token invalide** : Le token est malformé ou corrompu
- **Problème de cookies** : Les cookies HTTP-only ne sont pas correctement gérés

### **2. Vérifications à effectuer**

#### **A. Vérifier que le backend gère correctement les cookies**

Votre backend doit configurer les cookies JWT correctement :

```javascript
// Dans votre backend
const jwt = require('jsonwebtoken');

app.post('/api/user/login', async (req, res) => {
  try {
    // Vérifier les identifiants
    const user = await User.findOne({ email: req.body.email });
    if (!user || !await user.comparePassword(req.body.password)) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Créer le token JWT
    const token = jwt.sign(
      { userId: user._id },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );

    // Définir le cookie HTTP-only
    res.cookie('token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 24 * 60 * 60 * 1000 // 24 heures
    });

    res.json({
      message: 'Login successful',
      user: {
        id: user._id,
        email: user.email
      }
    });
  } catch (error) {
    res.status(500).json({ error: 'Server error' });
  }
});
```

#### **B. Vérifier la configuration CORS**

```javascript
const cors = require('cors');

app.use(cors({
  origin: true, // Ou spécifiez vos domaines autorisés
  credentials: true // Important pour les cookies
}));
```

#### **C. Vérifier le middleware d'authentification**

```javascript
const authenticateToken = (req, res, next) => {
  const token = req.cookies.token;
  
  if (!token) {
    return res.status(401).json({ error: 'Token missing' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(401).json({ error: 'Token invalid' });
    }
    req.user = user;
    next();
  });
};
```

### **3. Solutions côté frontend**

#### **A. Mode de test sans API**

Utilisez le bouton "Mode test (sans API)" sur l'écran de connexion en mode développement pour tester l'interface sans authentification.

#### **B. Vérifier les cookies dans le navigateur**

1. Ouvrez les outils de développement
2. Allez dans l'onglet "Application" ou "Storage"
3. Vérifiez que le cookie `token` est présent
4. Vérifiez sa valeur et sa date d'expiration

#### **C. Logs de débogage**

Activez les logs détaillés dans `src/config/config.js` :

```javascript
development: {
  API_BASE_URL: 'http://********:5000/api',
  DEBUG: true,
},
```

### **4. Tests de diagnostic**

#### **A. Test de connexion manuel**

```bash
# Test de connexion
curl -X POST http://localhost:5000/api/user/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Test123!"}' \
  -c cookies.txt

# Test avec le cookie
curl http://localhost:5000/api/user/profile \
  -b cookies.txt
```

#### **B. Test de l'endpoint de santé**

```bash
curl http://localhost:5000/health
```

### **5. Problèmes courants et solutions**

#### **Problème : "Token missing"**
**Cause :** L'utilisateur n'est pas connecté
**Solution :** Rediriger vers l'écran de connexion

#### **Problème : "Token expired"**
**Cause :** Le token JWT a expiré
**Solution :** Déconnecter l'utilisateur et demander une nouvelle connexion

#### **Problème : "Token invalid"**
**Cause :** Le token est corrompu ou malformé
**Solution :** Nettoyer les cookies et redemander une connexion

#### **Problème : "CORS error"**
**Cause :** Configuration CORS incorrecte
**Solution :** Vérifier la configuration CORS du backend

### **6. Débogage étape par étape**

1. **Vérifiez les logs du backend** pour voir les erreurs côté serveur
2. **Vérifiez les logs du frontend** pour voir les erreurs côté client
3. **Testez l'API manuellement** avec curl ou Postman
4. **Vérifiez la configuration** des cookies et CORS
5. **Testez avec le mode de développement** de l'application

### **7. Configuration recommandée**

#### **Backend (Node.js/Express)**
```javascript
// package.json
{
  "dependencies": {
    "express": "^4.17.1",
    "jsonwebtoken": "^8.5.1",
    "cookie-parser": "^1.4.5",
    "cors": "^2.8.5"
  }
}

// app.js
const express = require('express');
const cookieParser = require('cookie-parser');
const cors = require('cors');
const jwt = require('jsonwebtoken');

const app = express();

app.use(cookieParser());
app.use(cors({
  origin: true,
  credentials: true
}));
app.use(express.json());

// Middleware d'authentification
const authenticateToken = (req, res, next) => {
  const token = req.cookies.token;
  
  if (!token) {
    return res.status(401).json({ error: 'Token missing' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(401).json({ error: 'Token invalid' });
    }
    req.user = user;
    next();
  });
};

// Routes
app.post('/api/user/login', loginHandler);
app.get('/api/user/profile', authenticateToken, profileHandler);
```

#### **Frontend (React Native)**
```javascript
// Configuration
const config = {
  development: {
    API_BASE_URL: 'http://********:5000/api', // Android
    DEBUG: true,
  }
};

// Service API
const request = async (endpoint, options = {}) => {
  const config = {
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include', // Important pour les cookies
  };
  
  // ... reste du code
};
```

### **8. Support**

Si le problème persiste :

1. **Vérifiez la version** de votre backend et frontend
2. **Testez avec Postman** pour isoler le problème
3. **Vérifiez les variables d'environnement** (JWT_SECRET, etc.)
4. **Consultez les logs** détaillés
5. **Testez avec le mode de développement** de l'application 