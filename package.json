{"name": "awardsapp", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo-google-fonts/poppins": "^0.4.0", "@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@iconify-icons/solar": "^1.2.3", "@iconify/react": "^6.0.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.4.4", "@react-navigation/native": "^7.1.16", "@react-navigation/stack": "^7.4.4", "@solar-icons/react": "^1.0.1", "expo": "~53.0.20", "expo-font": "~13.3.2", "expo-linear-gradient": "~14.1.5", "expo-status-bar": "~2.2.3", "fbjs": "^3.0.5", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-iconify": "^2.0.3", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-vector-icons": "^10.3.0", "react-native-web": "^0.20.0", "expo-blur": "~14.1.5"}, "devDependencies": {"@babel/core": "^7.20.0"}, "overrides": {"react": "19.0.0", "react-dom": "19.0.0"}, "private": true}