# Guide de dépannage - Problèmes de connexion réseau

## 🔍 **Diagnostic des erreurs "Network request failed"**

### **1. Vérifier que le backend est démarré**

```bash
# Vérifier si le port 5000 est utilisé
netstat -an | findstr :5000

# Ou utiliser curl pour tester
curl http://localhost:5000/health
```

### **2. URLs correctes selon la plateforme**

#### **Pour Android :**
```javascript
API_BASE_URL: 'http://********:5000/api'
```

#### **Pour iOS :**
```javascript
API_BASE_URL: 'http://localhost:5000/api'
```

#### **Pour le web :**
```javascript
API_BASE_URL: 'http://localhost:5000/api'
```

### **3. Problèmes courants et solutions**

#### **Problème : "Network request failed"**
**Solutions :**
- Vérifiez que le backend est démarré sur le port 5000
- Vérifiez l'URL dans `src/config/config.js`
- Redémarrez l'application après avoir changé l'URL

#### **Problème : "Connection refused"**
**Solutions :**
- Le backend n'est pas démarré
- Le port 5000 est occupé par une autre application
- Firewall bloque la connexion

#### **Problème : "CORS error"**
**Solutions :**
- Vérifiez la configuration CORS du backend
- Assurez-vous que le backend accepte les requêtes depuis l'origine de l'app

#### **Problème : "Timeout"**
**Solutions :**
- Le serveur met trop de temps à répondre
- Problème de réseau
- Serveur surchargé

### **4. Utilisation du composant de test**

Le composant `ConnectionTest` est automatiquement affiché en mode développement sur l'écran d'accueil. Utilisez-le pour :

1. **Tester la connexion** à l'API
2. **Voir l'URL actuelle** utilisée
3. **Obtenir des messages d'erreur** détaillés
4. **Vérifier la configuration** de l'environnement

### **5. Configuration du backend**

Assurez-vous que votre backend :

1. **Écoute sur le bon port :**
```javascript
app.listen(5000, () => {
  console.log('Server running on port 5000');
});
```

2. **Accepte les requêtes CORS :**
```javascript
const cors = require('cors');
app.use(cors({
  origin: true,
  credentials: true
}));
```

3. **A un endpoint de santé :**
```javascript
app.get('/health', (req, res) => {
  res.json({ status: 'OK' });
});
```

### **6. Test manuel de l'API**

#### **Test avec curl :**
```bash
# Test de santé
curl http://localhost:5000/health

# Test des catégories
curl http://localhost:5000/api/category

# Test des stars
curl http://localhost:5000/api/star
```

#### **Test avec Postman :**
1. Créez une nouvelle requête GET
2. URL : `http://localhost:5000/health`
3. Envoyez la requête
4. Vérifiez la réponse

### **7. Logs de débogage**

Activez les logs détaillés dans `src/config/config.js` :

```javascript
development: {
  API_BASE_URL: 'http://********:5000/api',
  DEBUG: true, // Active les logs détaillés
},
```

### **8. Redémarrage complet**

Si rien ne fonctionne, essayez un redémarrage complet :

1. **Arrêtez l'application** React Native
2. **Arrêtez le backend**
3. **Redémarrez le backend**
4. **Redémarrez l'application** React Native
5. **Testez la connexion** avec le composant de test

### **9. Vérification du réseau**

#### **Pour Android :**
- Vérifiez que l'émulateur a accès au réseau
- Testez avec `adb shell ping ********`

#### **Pour iOS :**
- Vérifiez que le simulateur a accès au réseau
- Testez avec `ping localhost`

### **10. Fallback automatique**

L'application utilise maintenant des données de fallback si l'API n'est pas disponible. Cela permet de :

- **Tester l'interface** même sans backend
- **Développer** sans dépendre du serveur
- **Avoir une expérience utilisateur** même en cas de problème réseau

### **11. Support**

Si le problème persiste :

1. **Vérifiez les logs** de l'application
2. **Vérifiez les logs** du backend
3. **Testez avec curl** ou Postman
4. **Vérifiez la configuration** réseau
5. **Redémarrez** tous les services 