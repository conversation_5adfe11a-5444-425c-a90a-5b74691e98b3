// Configuration de l'application
const config = {
  // URL de l'API backend
  API_BASE_URL: 'http://localhost:5000/api',
  
  // Configuration pour le développement
  development: {
    API_BASE_URL: 'http://********:5000/api', // Pour Android
    DEBUG: true,
  },
  
  // Configuration pour la production
  production: {
    API_BASE_URL: 'https://your-production-api.com/api',
    DEBUG: false,
  },
  
  // Configuration pour les tests
  test: {
    API_BASE_URL: 'http://localhost:5000/api',
    DEBUG: true,
  },
};

// Déterminer l'environnement
const getEnvironment = () => {
  if (__DEV__) {
    return 'development';
  }
  // Vous pouvez ajouter d'autres conditions pour détecter l'environnement
  return 'development';
};

// Exporter la configuration pour l'environnement actuel
const currentConfig = config[getEnvironment()];

export default {
  ...config,
  ...currentConfig,
  environment: getEnvironment(),
}; 