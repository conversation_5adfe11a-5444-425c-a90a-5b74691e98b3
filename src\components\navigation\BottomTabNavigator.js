import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Image } from 'react-native';
import { BlurView } from 'expo-blur';

// Import screens
import HomeScreen from '../../screens/HomeScreen';
import DashboardScreen from '../../screens/DashboardScreen';
import HistoryScreen from '../../screens/HistoryScreen';

const Tab = createBottomTabNavigator();

const BottomTabNavigator = () => {
	return (
		<Tab.Navigator
			screenOptions={({ route }) => ({
				headerShown: false,
				tabBarIcon: ({ focused }) => {
					let iconSource;

					if (route.name === 'Home') {
						iconSource = focused
							? require('../../../assets/icons/home.png')
							: require('../../../assets/icons/homeOutline.png');
					} else if (route.name === 'Dashboard') {
						iconSource = focused
							? require('../../../assets/icons/heart.png')
							: require('../../../assets/icons/heartOutline.png');
					} else if (route.name === 'Profile') {
						iconSource = focused
							? require('../../../assets/icons/user.png')
							: require('../../../assets/icons/userOutline.png');
					}

					return (
						<Image
							source={iconSource}
							style={{
								width: 26,
								height: 26,
								tintColor: focused ? '#DEB887' : '#A0522D'
							}}
							resizeMode="contain"
						/>
					);
				},
				tabBarActiveTintColor: '#DEB887',
				tabBarInactiveTintColor: '#A0522D',
				tabBarShowLabel: false,
				tabBarStyle: {
					position: 'absolute',
					height: 80,
					borderRadius: 25,
					marginHorizontal: 50,
					marginBottom: 34,
					overflow: 'hidden',
					borderTopWidth: 0,
					paddingTop: 20,
				},
				tabBarBackground: () => (
					<BlurView
						tint="dark"
						intensity={70}
						style={{ flex: 1, backgroundColor: 'rgba(47, 27, 20, 0.8)' }}
					/>
				),
			})}
		>
			<Tab.Screen name="Home" component={HomeScreen} />
			<Tab.Screen name="Dashboard" component={DashboardScreen} />
			<Tab.Screen name="Profile" component={HistoryScreen} />
		</Tab.Navigator>
	);
};

export default BottomTabNavigator;
