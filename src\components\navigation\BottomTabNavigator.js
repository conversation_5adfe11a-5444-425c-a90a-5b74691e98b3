import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';

// Import screens
import HomeScreen from '../../screens/HomeScreen';
import DashboardScreen from '../../screens/DashboardScreen';
import HistoryScreen from '../../screens/HistoryScreen';

const Tab = createBottomTabNavigator();

const BottomTabNavigator = () => {
	return (
		<Tab.Navigator
			screenOptions={({ route }) => ({
				headerShown: false,
				tabBarIcon: ({ focused, color }) => {
					let iconName;

					if (route.name === 'Home') {
						iconName = focused ? 'home' : 'home-outline';
					} else if (route.name === 'Dashboard') {
						iconName = focused ? 'heart' : 'heart-outline';
					} else if (route.name === 'Profile') {
						iconName = focused ? 'person' : 'person-outline';
					}

					return <Ionicons name={iconName} size={26} color={color} />;
				},
				tabBarActiveTintColor: '#fff',
				tabBarInactiveTintColor: '#aaa',
				tabBarShowLabel: false,
				tabBarStyle: {
					position: 'absolute',
					height: 80,
					borderRadius: 25,
					marginHorizontal: 50,
					marginBottom: 34,
					overflow: 'hidden', // important to clip the blur
					borderTopWidth: 0,
					paddingTop: 20,
				},
				tabBarBackground: () => (
					<BlurView
						tint="dark" // gives the glass effect
						intensity={60} // 0 = transparent, 100 = strong blur
						style={{ flex: 1, backgroundColor: 'rgba(28, 28, 28, 0.65)' }}
						// ^ overlay a brownish transparent background
					/>
				),
			})}
		>
			<Tab.Screen name="Home" component={HomeScreen} />
			<Tab.Screen name="Dashboard" component={DashboardScreen} />
			<Tab.Screen name="Profile" component={HistoryScreen} />
		</Tab.Navigator>
	);
};

export default BottomTabNavigator;
