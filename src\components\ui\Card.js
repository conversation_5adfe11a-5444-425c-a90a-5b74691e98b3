import React from 'react';
import {
  View,
  StyleSheet,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import theme from '../../theme/theme';

const Card = ({
  children,
  variant = 'primary',
  style,
  ...props
}) => {
  const getCardStyle = () => {
    const baseStyle = [styles.card, styles[variant]];
    
    if (style) {
      baseStyle.push(style);
    }
    
    return baseStyle;
  };

  const renderCard = () => {
    if (variant === 'glass') {
      return (
        <LinearGradient
          colors={theme.colors.gradients.glass}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={getCardStyle()}
          {...props}
        >
          {children}
        </LinearGradient>
      );
    }

    if (variant === 'accent') {
      return (
        <LinearGradient
          colors={theme.colors.gradients.blackToGold}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={getCardStyle()}
          {...props}
        >
          {children}
        </LinearGradient>
      );
    }

    return (
      <View style={getCardStyle()} {...props}>
        {children}
      </View>
    );
  };

  return renderCard();
};

const styles = StyleSheet.create({
  card: {
    borderRadius: theme.borderRadius.xl,
    padding: theme.spacing.lg,
  },
  
  // Variants
  primary: {
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.surfaceLight,
    ...theme.shadows.md,
  },
  accent: {
    borderWidth: 1,
    borderColor: theme.colors.gold,
    ...theme.shadows.gold,
  },
  elevated: {
    backgroundColor: theme.colors.surface,
    ...theme.shadows.lg,
  },
  transparent: {
    backgroundColor: 'transparent',
  },
  outline: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.textTertiary,
  },
  glass: {
    borderWidth: 1,
    borderColor: 'rgba(255, 215, 0, 0.3)',
    backdropFilter: 'blur(10px)',
  },
});

export default Card;
