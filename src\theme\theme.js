// Thème inspiré des designs modernes - Awards App
const theme = {
  // Palette de couleurs principale
  colors: {
    // Couleurs de base - Brown/Bronze Theme
    primary: '#8B4513', // Brun selle
    secondary: '#A0522D', // Brun sienna
    accent: '#CD853F', // Brun peru
    accentLight: '#DEB887', // Brun burlywood

    // Couleurs de fond
    background: '#2F1B14', // Brun très sombre
    surface: '#3E2723', // Brun sombre
    surfaceLight: '#5D4037', // Brun moyen
    surfaceDark: '#1A0E0A', // Brun très profond

    // Couleurs de texte
    textPrimary: '#FFFFFF', // Blanc pur
    textSecondary: '#F5F5DC', // Beige clair
    textTertiary: '#D2B48C', // Tan
    textMuted: '#A0522D', // Brun sienna
    textButton: '#2F1B14', // Brun sombre pour boutons
    textAccent: '#CD853F', // Brun peru pour accents

    // Couleurs d'état
    success: '#8FBC8F', // Vert sombre
    warning: '#DAA520', // Or sombre
    error: '#CD5C5C', // Rouge indien
    info: '#4682B4', // Bleu acier

    // Couleurs spéciales
    gold: '#DAA520', // Or sombre
    goldLight: '#F0E68C', // Kaki
    goldDark: '#B8860B', // Or sombre foncé
    bronze: '#CD7F32', // Bronze
    copper: '#B87333', // Cuivre

    // Gradients
    gradients: {
      primary: ['#8B4513', '#A0522D', '#CD853F'],
      background: ['#2F1B14', '#3E2723', '#5D4037'],
      accent: ['#CD853F', '#DEB887', '#F5DEB3'],
      dark: ['#1A0E0A', '#2F1B14', '#3E2723'],
      gold: ['#DAA520', '#F0E68C', '#DAA520'],
      bronze: ['#CD7F32', '#B87333', '#8B4513'],
      glass: ['rgba(47, 27, 20, 0.9)', 'rgba(62, 39, 35, 0.7)', 'rgba(205, 133, 63, 0.1)'],
      cta: ['#DEB887', '#CD853F', '#A0522D'],
      card: ['rgba(93, 64, 55, 0.8)', 'rgba(62, 39, 35, 0.9)'],
      overlay: ['rgba(26, 14, 10, 0.8)', 'rgba(47, 27, 20, 0.6)'],
    }
  },

  // Typographie
  typography: {
    // Familles de polices
    fontFamily: {
      primary: 'System', // Police système par défaut
      secondary: 'System',
      accent: 'System',
    },
    
    // Tailles de police
    fontSize: {
      xs: 10,
      sm: 12,
      base: 14,
      lg: 16,
      xl: 18,
      '2xl': 20,
      '3xl': 24,
      '4xl': 28,
      '5xl': 32,
    },
    
    // Poids de police
    fontWeight: {
      light: '300',
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
      extrabold: '800',
    },
    
    // Hauteur de ligne
    lineHeight: {
      tight: 1.2,
      normal: 1.4,
      relaxed: 1.6,
      loose: 1.8,
    }
  },

  // Espacement
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    '2xl': 48,
    '3xl': 64,
  },

  // Bordures
  borderRadius: {
    none: 0,
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    '2xl': 20,
    '3xl': 50,
    full: 9999,
  },

  // Ombres
  shadows: {
    sm: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.2,
      shadowRadius: 2,
      elevation: 2,
    },
    md: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 4,
    },
    lg: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.4,
      shadowRadius: 16,
      elevation: 8,
    },
    gold: {
      shadowColor: '#ffd700',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 4,
    }
  },

  // Composants
  components: {
    // Boutons
    button: {
      primary: {
        backgroundColor: '#ffd700',
        color: '#1a1a1a',
        borderRadius: 16,
        paddingVertical: 16,
        paddingHorizontal: 24,
        fontSize: 16,
        fontWeight: '600',
      },
      secondary: {
        backgroundColor: 'transparent',
        color: '#ffd700',
        borderWidth: 2,
        borderColor: '#ffd700',
        borderRadius: 16,
        paddingVertical: 16,
        paddingHorizontal: 24,
        fontSize: 16,
        fontWeight: '600',
      },
      outline: {
        backgroundColor: 'transparent',
        color: '#ffffff',
        borderWidth: 1,
        borderColor: '#b0b0b0',
        borderRadius: 16,
        paddingVertical: 14,
        paddingHorizontal: 20,
        fontSize: 14,
        fontWeight: '500',
      }
    },

    // Cartes
    card: {
      primary: {
        backgroundColor: '#0a0a0a',
        borderRadius: 20,
        padding: 20,
        borderWidth: 1,
        borderColor: '#1a1a1a',
      },
      accent: {
        backgroundColor: '#1a1a1a',
        borderRadius: 20,
        padding: 20,
        borderWidth: 1,
        borderColor: '#ffd700',
      },
      elevated: {
        backgroundColor: '#0a0a0a',
        borderRadius: 20,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 4,
      }
    },

    // Inputs style par défaut (utile si besoin)
    input: {
      primary: {
        backgroundColor: '#1a1a1a',
        borderWidth: 1,
        borderColor: '#2a2a2a',
        borderRadius: 16,
        paddingVertical: 14,
        paddingHorizontal: 16,
        color: '#ffffff',
        fontSize: 16,
      },
      focused: {
        backgroundColor: '#1a1a1a',
        borderWidth: 2,
        borderColor: '#ffd700',
        borderRadius: 16,
        paddingVertical: 14,
        paddingHorizontal: 16,
        color: '#ffffff',
        fontSize: 16,
      }
    },

    // Navigation
    navigation: {
      header: {
        backgroundColor: '#000000',
        borderBottomWidth: 1,
        borderBottomColor: '#1a1a1a',
      },
      tabBar: {
        backgroundColor: '#000000',
        borderTopWidth: 1,
        borderTopColor: '#1a1a1a',
      }
    }
  },

  // Animations
  animations: {
    duration: {
      fast: 200,
      normal: 300,
      slow: 500,
    },
    easing: {
      ease: 'ease',
      easeIn: 'ease-in',
      easeOut: 'ease-out',
      easeInOut: 'ease-in-out',
    }
  }
};

export default theme;
