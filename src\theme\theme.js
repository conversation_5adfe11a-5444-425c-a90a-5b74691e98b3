// Thème inspiré des designs modernes - Awards App
const theme = {
  // Palette de couleurs principale
  colors: {
    // Couleurs de base
    primary: '#000000', // Noir pur
    secondary: '#1a1a1a', // Noir profond
    accent: '#ffd700', // Or élégant
    accentLight: '#ffed4e', // Or plus clair
    
    // Couleurs de fond
    background: '#000000', // Noir pur
    surface: '#0a0a0a', // Noir très sombre
    surfaceLight: '#6f5f4d', // Noir profond
    surfaceDark: '#000000', // Noir pur

    
    // Couleurs de texte
    textPrimary: '#ffffff', // Blanc pur
    textSecondary: '#c4c4c4', // Blanc cassé
    textTertiary: '#b0b0b0', // Gris clair
    textMuted: '#808080', // Gris moyen
    textButton: '#333640', // Noir pour les boutons
    textclick: '#d9c3a2', // Jaune cassis
    
    // Couleurs d'état
    success: '#4CAF50', // Vert
    warning: '#FF9800', // Orange
    error: '#f44336', // Rouge
    info: '#2196F3', // Bleu
    
    // Couleurs spéciales
    gold: '#ffd700',
    goldLight: '#ffed4e',
    goldDark: '#b8860b',
    black: '#000000',
    blackLight: '#1a1a1a',
    blackDark: '#000000',
    
    // Gradients
    gradients: {
      primary: ['#74573A', '#49392a', '#433529', '#372B2B'],
      accent: ['#ffd700', '#ffed4e', '#ffd700'],
      dark: ['#000000', '#0a0a0a', '#1a1a1a'],
      gold: ['#ffd700', '#b8860b', '#ffd700'],
      blackToGold: ['#000000', '#1a1a1a', '#ffd700'],
      goldToBlack: ['#ffd700', '#b8860b', '#000000'],
      glass: ['rgba(0, 0, 0, 0.9)', 'rgba(0, 0, 0, 0.7)', 'rgba(255, 215, 0, 0.1)'],
      cta: ['#EEDABC', '#987952'],
    }
  },

  // Typographie
  typography: {
    // Familles de polices
    fontFamily: {
      primary: 'System', // Police système par défaut
      secondary: 'System',
      accent: 'System',
    },
    
    // Tailles de police
    fontSize: {
      xs: 10,
      sm: 12,
      base: 14,
      lg: 16,
      xl: 18,
      '2xl': 20,
      '3xl': 24,
      '4xl': 28,
      '5xl': 32,
    },
    
    // Poids de police
    fontWeight: {
      light: '300',
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
      extrabold: '800',
    },
    
    // Hauteur de ligne
    lineHeight: {
      tight: 1.2,
      normal: 1.4,
      relaxed: 1.6,
      loose: 1.8,
    }
  },

  // Espacement
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    '2xl': 48,
    '3xl': 64,
  },

  // Bordures
  borderRadius: {
    none: 0,
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    '2xl': 20,
    '3xl': 50,
    full: 9999,
  },

  // Ombres
  shadows: {
    sm: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.2,
      shadowRadius: 2,
      elevation: 2,
    },
    md: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 4,
    },
    lg: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.4,
      shadowRadius: 16,
      elevation: 8,
    },
    gold: {
      shadowColor: '#ffd700',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 4,
    }
  },

  // Composants
  components: {
    // Boutons
    button: {
      primary: {
        backgroundColor: '#ffd700',
        color: '#1a1a1a',
        borderRadius: 16,
        paddingVertical: 16,
        paddingHorizontal: 24,
        fontSize: 16,
        fontWeight: '600',
      },
      secondary: {
        backgroundColor: 'transparent',
        color: '#ffd700',
        borderWidth: 2,
        borderColor: '#ffd700',
        borderRadius: 16,
        paddingVertical: 16,
        paddingHorizontal: 24,
        fontSize: 16,
        fontWeight: '600',
      },
      outline: {
        backgroundColor: 'transparent',
        color: '#ffffff',
        borderWidth: 1,
        borderColor: '#b0b0b0',
        borderRadius: 16,
        paddingVertical: 14,
        paddingHorizontal: 20,
        fontSize: 14,
        fontWeight: '500',
      }
    },

    // Cartes
    card: {
      primary: {
        backgroundColor: '#0a0a0a',
        borderRadius: 20,
        padding: 20,
        borderWidth: 1,
        borderColor: '#1a1a1a',
      },
      accent: {
        backgroundColor: '#1a1a1a',
        borderRadius: 20,
        padding: 20,
        borderWidth: 1,
        borderColor: '#ffd700',
      },
      elevated: {
        backgroundColor: '#0a0a0a',
        borderRadius: 20,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 4,
      }
    },

    // Inputs style par défaut (utile si besoin)
    input: {
      primary: {
        backgroundColor: '#1a1a1a',
        borderWidth: 1,
        borderColor: '#2a2a2a',
        borderRadius: 16,
        paddingVertical: 14,
        paddingHorizontal: 16,
        color: '#ffffff',
        fontSize: 16,
      },
      focused: {
        backgroundColor: '#1a1a1a',
        borderWidth: 2,
        borderColor: '#ffd700',
        borderRadius: 16,
        paddingVertical: 14,
        paddingHorizontal: 16,
        color: '#ffffff',
        fontSize: 16,
      }
    },

    // Navigation
    navigation: {
      header: {
        backgroundColor: '#000000',
        borderBottomWidth: 1,
        borderBottomColor: '#1a1a1a',
      },
      tabBar: {
        backgroundColor: '#000000',
        borderTopWidth: 1,
        borderTopColor: '#1a1a1a',
      }
    }
  },

  // Animations
  animations: {
    duration: {
      fast: 200,
      normal: 300,
      slow: 500,
    },
    easing: {
      ease: 'ease',
      easeIn: 'ease-in',
      easeOut: 'ease-out',
      easeInOut: 'ease-in-out',
    }
  }
};

export default theme;
