import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import Input from '../components/ui/Input';
import GradientView from '../components/ui/GradientView';
import theme from '../theme/theme';
import userIcon from '../../assets/icons/user.png';
import lockIcon from '../../assets/icons/lock.png';
import emailIcon from '../../assets/icons/letter.png';
import phoneIcon from '../../assets/icons/phone.png';
import googleIcon from '../../assets/icons/google.png';

const AuthScreen = ({ navigation }) => {
  const { login, register, loading } = useAuth();
  const [isLogin, setIsLogin] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
  });
  const [localLoading, setLocalLoading] = useState(false);

  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone) => {
    const phoneRegex = /^[0-9]{10}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
  };

  const validatePassword = (password) => {
    // Au moins 8 caractères, 1 majuscule, 1 chiffre, 1 caractère spécial
    const passwordRegex = /^(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    return passwordRegex.test(password);
  };

  const handleSubmit = async () => {
    if (!isLogin) {
      // Validation pour l'inscription
      if (!formData.firstName.trim()) {
        Alert.alert('Erreur', 'Le prénom est requis');
        return;
      }
      if (!formData.lastName.trim()) {
        Alert.alert('Erreur', 'Le nom est requis');
        return;
      }
      if (!validatePassword(formData.password)) {
        Alert.alert('Erreur', 'Le mot de passe doit contenir au moins 8 caractères, 1 majuscule, 1 chiffre et 1 caractère spécial');
        return;
      }
    }

    if (!validateEmail(formData.email)) {
      Alert.alert('Erreur', 'Veuillez entrer une adresse email valide');
      return;
    }

    if (!isLogin && !validatePhone(formData.phone)) {
      Alert.alert('Erreur', 'Veuillez entrer un numéro de téléphone valide (10 chiffres)');
      return;
    }

    if (isLogin && !formData.password) {
      Alert.alert('Erreur', 'Le mot de passe est requis');
      return;
    }

    setLocalLoading(true);

    try {
      if (isLogin) {
        await login({
          email: formData.email,
          password: formData.password,
        });
      } else {
        await register({
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone,
          password: formData.password,
        });
      }

      Alert.alert(
        'Succès',
        isLogin ? 'Connexion réussie !' : 'Inscription réussie !',
        [
          {
            text: 'OK',
            onPress: () => navigation.navigate('Dashboard'),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Erreur', error.message || 'Une erreur est survenue. Veuillez réessayer.');
    } finally {
      setLocalLoading(false);
    }
  };

  const updateFormData = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <GradientView gradient="premium" style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardView}
        >
          <ScrollView contentContainerStyle={styles.scrollContent}>

          <Card variant="transparent" style={styles.header}>
            <Text style={styles.title}>
              {isLogin ? 'Se connecter sur leaders' : 'Créer un compte sur leaders'}
            </Text>
            <Text style={styles.subtitle}>
              {isLogin 
                ? 'Inscrivez-vous sur Leaders Awards  pour donner votre avis sur vos célébrités préférées.'
                : 'Inscrivez-vous sur Leaders Awards  pour donner votre avis sur vos célébrités préférées.'
              }
            </Text>
          </Card>  
          {/* <Card variant="glass" style={styles.header}>
            <Ionicons name="person-circle" size={80} color={theme.colors.gold} />
            <Text style={styles.title}>
              {isLogin ? 'Connexion' : 'Inscription'}
            </Text>
            <Text style={styles.subtitle}>
              {isLogin 
                ? 'Connectez-vous pour accéder à vos votes'
                : 'Créez votre compte pour participer aux votes'
              }
            </Text>
          </Card> */}

          <Card variant="transparent" style={styles.formContainer}>
            {!isLogin && (
              <>
                <Input
                  label="Prénom"
                  placeholder="Votre prénom"
                  value={formData.firstName}
                  onChangeText={(text) => updateFormData('firstName', text)}
                  icon={userIcon}
                  autoCapitalize="words"
                />

                <Input
                  label="Nom"
                  placeholder="Votre nom"
                  value={formData.lastName}
                  onChangeText={(text) => updateFormData('lastName', text)}
                  icon={userIcon}
                  autoCapitalize="words"
                />
              </>
            )}

            <Input
              label="Email"
              placeholder="<EMAIL>"
              value={formData.email}
              onChangeText={(text) => updateFormData('email', text)}
              icon={emailIcon}
              keyboardType="email-address"
              autoCapitalize="none"
            />

            <Input
              label="Mot de passe"
              placeholder="Votre mot de passe"
              value={formData.password}
              onChangeText={(text) => updateFormData('password', text)}
              icon={lockIcon}
              secureTextEntry
              autoCapitalize="none"
            />

            {!isLogin && (
              <Input
                label="Téléphone"
                placeholder="0123456789"
                value={formData.phone}
                onChangeText={(text) => updateFormData('phone', text)}
                icon={phoneIcon}
                keyboardType="phone-pad"
              />
            )}

            <Button
              title={isLogin ? 'Se connecter' : "Créer un compte"}
              onPress={handleSubmit}
              variant="primary"
              size="small"
              textStyle={{ fontSize: theme.typography.fontSize.lg ,padding: theme.spacing.xs }}
              loading={loading || localLoading}
              //icon={isLogin ? "log-in" : "person-add"}
              style={styles.submitButton}
            />
            {!isLogin && (
              <>
            {/* Séparateur OR */}
            <View style={{ marginTop: 16 }}>
              <View style={{ height: 1, backgroundColor: theme.colors.surfaceLight }} />
              <View style={{ position: 'absolute', alignSelf: 'center', top: -10, backgroundColor: 'transparent', paddingHorizontal: 8 }}>
                <Text style={{ color: theme.colors.textTertiary, fontFamily: 'Poppins_600SemiBold', fontSize: 12 }}>OU</Text>
              </View>
            </View>
            </>
            )}
          </Card>

          {!isLogin && (
          <View variant="transparent" style={styles.socialSection}>
            <View style={styles.socialButtons}>
              <Button
                title="Continuer avec Google"
                textStyle={{ color: theme.colors.black,padding: theme.spacing.xs,fontSize: theme.typography.fontSize.lg }}
                variant="outline"
                size="small"
                icon={googleIcon}
                iconPosition="left"
                style={styles.socialButton}
              />
              
            </View>
          </View>
          )}
          <TouchableOpacity
              style={styles.switchButton}
              onPress={() => setIsLogin(!isLogin)}
            >
              <Text style={styles.switchButtonText}>
                {isLogin 
                  ? "Pas encore de compte ? S'inscrire"
                  : "Déjà un compte ? Se connecter"
                }
              </Text>
            </TouchableOpacity>

          
        </ScrollView>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: theme.spacing.md,
  },
  header: {
    alignItems: 'left',
    //marginBottom: theme.spacing.xs,
  },
  title: {
    fontSize: theme.typography.fontSize['3xl'],
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.textPrimary,
    marginTop: theme.spacing.md,
    //marginBottom: theme.spacing.sm,
  },
  subtitle: {
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.textSecondary,
    lineHeight: 25,
  },
  formContainer: {
    //marginBottom: theme.spacing.xl,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1a1a2e',
    borderRadius: 12,
    //marginBottom: 15,
    paddingHorizontal: 15,
    borderWidth: 1,
    borderColor: '#2a2a3e',
  },
  inputIcon: {
    marginRight: 10,
  },
  input: {
    flex: 1,
    color: '#fff',
    fontSize: 16,
    paddingVertical: 15,
  },
  submitButton: {
   // marginTop: theme.spacing.md,
    borderRadius: 50,
  },
  switchButton: {
    marginTop: theme.spacing.md,
    alignItems: 'center',
  },
  switchButtonText: {
    color: theme.colors.textclick,
    fontSize: theme.typography.fontSize.base,
  },
  socialSection: {
    alignItems: 'center',
  },
  socialTitle: {
    color: theme.colors.textSecondary,
    fontSize: theme.typography.fontSize.base,
    //marginBottom: theme.spacing.md,
  },
  socialButtons: {
    flexDirection: 'row',
    //justifyContent: 'center',
    //width: '100%',
    paddingHorizontal: theme.spacing.lg,

  },
  socialButton: {
    flex: 1,
    borderRadius: 50,
    backgroundColor: theme.colors.textPrimary,
  },
  
  
});

export default AuthScreen;
