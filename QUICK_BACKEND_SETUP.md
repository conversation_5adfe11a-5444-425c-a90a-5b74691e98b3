# Guide de démarrage rapide - Backend

## 🚀 **Démarrage immédiat**

### **1. Vérifiez que votre backend est démarré**

```bash
# Dans le dossier de votre backend (pas awardsApp)
cd ../backend  # ou le nom de votre dossier backend
npm start
```

### **2. Vérifiez le port 5000**

```bash
# Vérifier si le port 5000 est utilisé
netstat -an | findstr :5000
```

### **3. Test rapide avec curl**

```bash
# Test de santé
curl http://localhost:5000/health

# Test de connexion
curl -X POST http://localhost:5000/api/user/login \
  -H "Content-Type: application/json" \
  -d "{\"email\":\"<EMAIL>\",\"password\":\"Test123!\"}"
```

## 🔧 **Corrections rapides**

### **A. Si le serveur ne démarre pas**

1. **Vérifiez les dépendances** :
```bash
npm install
```

2. **Vérifiez le fichier principal** :
```bash
# Votre fichier principal doit être :
node server.js
# ou
npm start
```

### **B. Si CORS pose problème**

Ajoutez dans votre `app.js` ou `server.js` :

```javascript
const cors = require('cors');

app.use(cors({
  origin: true,
  credentials: true
}));
```

### **C. Si les cookies ne fonctionnent pas**

Ajoutez :

```javascript
const cookieParser = require('cookie-parser');
app.use(cookieParser());
```

### **D. Si l'authentification échoue**

Vérifiez votre middleware :

```javascript
const authenticateToken = (req, res, next) => {
  const token = req.cookies.token;
  
  if (!token) {
    return res.status(401).json({ error: 'Token missing' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(401).json({ error: 'Token invalid' });
    }
    req.user = user;
    next();
  });
};
```

## 📋 **Checklist de 5 minutes**

- [ ] **Backend démarré** sur le port 5000
- [ ] **CORS configuré** avec `credentials: true`
- [ ] **Cookie parser** installé et configuré
- [ ] **JWT_SECRET** défini dans les variables d'environnement
- [ ] **Test curl** fonctionne

## 🎯 **Test de l'application**

Une fois le backend corrigé :

1. **Redémarrez l'application** React Native
2. **Utilisez le composant BackendTest** sur l'écran d'accueil
3. **Testez la connexion** avec n'importe quel email/mot de passe

## 🆘 **Si rien ne fonctionne**

1. **Mode de test** : L'application utilise maintenant un mode de test en développement
2. **Continuez le développement** : Vous pouvez tester l'interface sans backend
3. **Corrigez le backend** : Utilisez le guide `BACKEND_CHECKLIST.md` pour les détails

## 📞 **Support**

Si vous avez besoin d'aide pour corriger le backend :

1. **Partagez les logs** de votre serveur
2. **Indiquez le framework** utilisé (Express, Fastify, etc.)
3. **Décrivez l'erreur** exacte rencontrée

Le frontend est maintenant prêt et fonctionnel ! 🎉 