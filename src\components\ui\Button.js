import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import theme from '../../theme/theme';

const Button = ({
  title,
  onPress,
  variant = 'primary',
  size = 'small',
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  style,
  textStyle,
  children,
}) => {
  const getButtonStyle = () => {
    const baseStyle = [styles.button, styles[variant], styles[size]];
    
    if (disabled) {
      baseStyle.push(styles.disabled);
    }
    
    if (style) {
      baseStyle.push(style);
    }
    
    return baseStyle;
  };

  const getTextStyle = () => {
    const baseTextStyle = [styles.text, styles[`${variant}Text`], styles[`${size}Text`]];
    
    if (disabled) {
      baseTextStyle.push(styles.disabledText);
    }
    
    if (textStyle) {
      baseTextStyle.push(textStyle);
    }
    
    return baseTextStyle;
  };

  const renderContent = () => {
    if (loading) {
      return (
        <>
                   <ActivityIndicator 
           size="small" 
           color={variant === 'primary' ? theme.colors.black : theme.colors.gold} 
         />
          <Text style={[getTextStyle(), styles.loadingText]}>Chargement...</Text>
        </>
      );
    }

    if (children) {
      return children;
    }

    return (
      <>
                 {icon && iconPosition === 'left' && (
           <Image
           source={icon}
           resizeMode="contain"
           style={[styles.leftIcon, { width: 20, height: 20}]}
         />
         )}
         <Text style={getTextStyle()}>{title}</Text>
         {icon && iconPosition === 'right' && (
           <Image 
           source={icon}
           resizeMode="contain"
           style={[styles.rightIcon, { width: 18, height: 18}]}
           />
         )}
      </>
    );
  };

  const renderButton = () => {
    if (variant === 'primary') {
      return (
        <LinearGradient
          colors={theme.colors.gradients.cta}
          start={{ x: 0, y: 0.5 }}
          end={{ x: 1, y: 0.5 }}
          style={getButtonStyle()}
        >
          <TouchableOpacity
            style={styles.touchable}
            onPress={onPress}
            disabled={disabled || loading}
            activeOpacity={0.8}
          >
            {renderContent()}
          </TouchableOpacity>
        </LinearGradient>
      );
    }

    return (
      <TouchableOpacity
        style={getButtonStyle()}
        onPress={onPress}
        disabled={disabled || loading}
        activeOpacity={0.8}
      >
        {renderContent()}
      </TouchableOpacity>
    );
  };

  return renderButton();
};

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    //borderRadius: theme.borderRadius['3xl'],
  },
  
  // Variants
  primary: {
   // ...theme.shadows.gold,
  },
  secondary: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: theme.colors.gold,
  },
  outline: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.textTertiary,
  },
  ghost: {
    backgroundColor: 'transparent',
  },
  
  // Sizes
  small: {
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
  },
  medium: {
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.lg,
    borderRadius: theme.borderRadius.lg,
  },
  large: {
    paddingVertical: theme.spacing.lg,
    paddingHorizontal: theme.spacing.xl,
    borderRadius: theme.borderRadius.xl,
  },
  
  // Text styles
  text: {
    fontWeight: theme.typography.fontWeight.semibold,
    textAlign: 'center',
  },
     primaryText: {
     color: theme.colors.textButton,
   },
  secondaryText: {
    color: theme.colors.black,
  },
  outlineText: {
    color: theme.colors.textButton,
  },
  ghostText: {
    color: theme.colors.textButton,
  },
  
  // Size text
  smallText: {
    fontSize: theme.typography.fontSize.sm,
  },
  mediumText: {
    fontSize: theme.typography.fontSize.lg,
  },
  largeText: {
    fontSize: theme.typography.fontSize.xl,
  },
  
  // States
  disabled: {
    opacity: 0.5,
  },
  disabledText: {
    opacity: 0.7,
  },
  
  // Icons
  leftIcon: {
    zIndex: 1,
    position: 'absolute',
    left: 16,
    top: 14,
    //marginRight: theme.spacing.sm,
  },
  rightIcon: {
    position: 'absolute',
    zIndex: 1,
    right: 16,
    top: 14,

    marginLeft: theme.spacing.sm,
  },
  
  // Loading
  loadingText: {
    marginLeft: theme.spacing.sm,
  },
  
  // Touchable for gradient buttons
  touchable: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default Button;
