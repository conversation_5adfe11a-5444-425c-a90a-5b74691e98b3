import config from '../config/config';
import AsyncStorage from '@react-native-async-storage/async-storage';

const API_BASE_URL = config.API_BASE_URL;

class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  // Méthode générique pour les requêtes
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    
    // Récupérer le token depuis AsyncStorage pour les headers Authorization
    let authToken = null;
    try {
      const userData = await AsyncStorage.getItem('userData');
      if (userData) {
        const parsed = JSON.parse(userData);
        authToken = parsed.token; // Si le token est stocké dans userData
        console.log('Token found in storage:', authToken ? 'Yes' : 'No');
      }
    } catch (error) {
      console.log('No auth token found in storage');
    }
    
    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Pour les cookies
    };

    // Ajouter le header Authorization si un token est disponible
    if (authToken) {
      defaultOptions.headers['Authorization'] = `Bearer ${authToken}`;
      console.log('Authorization header added');
    } else {
      console.log('No Authorization header added');
    }

    const config = {
      ...defaultOptions,
      ...options,
      headers: {
        ...defaultOptions.headers,
        ...options.headers,
      },
    };

    console.log('Making request to:', url);
    console.log('Request config:', {
      method: config.method || 'GET',
      headers: config.headers,
      credentials: config.credentials
    });

    try {
      const response = await fetch(url, config);
      
      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.log('Error response data:', errorData);
        
        // Gestion spécifique des erreurs d'authentification
        if (response.status === 401) {
          throw new Error('Token invalide ou expiré. Veuillez vous reconnecter.');
        }
        
        if (response.status === 403) {
          throw new Error('Accès interdit. Vérifiez vos permissions.');
        }
        
        throw new Error(errorData.error || errorData.message || `HTTP error! status: ${response.status}`);
      }

      const responseData = await response.json();
      console.log('Response data:', responseData);
      return responseData;
    } catch (error) {
      console.error('API Error:', error);
      
      // Gestion spécifique des erreurs de réseau
      if (error.message.includes('Network request failed')) {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que le backend est démarré sur le port 5000.');
      }
      
      if (error.message.includes('fetch')) {
        throw new Error('Erreur de connexion réseau. Vérifiez votre connexion internet.');
      }
      
      throw error;
    }
  }

  // Authentification
  async register(userData) {
    return this.request('/user/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async login(credentials) {
    return this.request('/user/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  }

  async logout() {
    return this.request('/user/logout', {
      method: 'POST',
    });
  }

  async getUserProfile() {
    return this.request('/user/profile');
  }

  async verifyToken() {
    return this.request('/user/verify-token');
  }

  // Catégories
  async getCategories() {
    return this.request('/category');
  }

  async createCategory(categoryData) {
    return this.request('/category', {
      method: 'POST',
      body: JSON.stringify(categoryData),
    });
  }

  async deleteCategory(categoryId) {
    return this.request(`/category/${categoryId}`, {
      method: 'DELETE',
    });
  }

  // Stars
  async getStars() {
    return this.request('/star');
  }

  async createStar(starData) {
    return this.request('/star', {
      method: 'POST',
      body: JSON.stringify(starData),
    });
  }

  async toggleStarVisibility(starId) {
    return this.request(`/star/${starId}/visibility`, {
      method: 'PATCH',
    });
  }

  // Votes
  async getUserVotes(userId) {
    return this.request(`/user/votes/${userId}`);
  }

  async submitVote(voteData) {
    return this.request('/user/vote', {
      method: 'POST',
      body: JSON.stringify(voteData),
    });
  }

  // Santé du serveur
  async checkHealth() {
    // L'endpoint de santé est à la racine, pas dans /api
    const url = `${this.baseURL.replace('/api', '')}/health`;
    
    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    };

    try {
      const response = await fetch(url, defaultOptions);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Health check error:', error);
      throw error;
    }
  }
}

// Instance singleton
const apiService = new ApiService();

export default apiService; 