import React, { useState } from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image ,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
//import Icon from 'react-native-iconify';



import theme from '../../theme/theme';

const Input = ({
  label,
  placeholder,
  value,
  onChangeText,
  secureTextEntry = false,
  keyboardType = 'default',
  autoCapitalize = 'sentences',
  autoCorrect = true,
  multiline = false,
  numberOfLines = 1,
  icon,
  iconPosition = 'left',
  error,
  disabled = false,
  style,
  rightAccessory, // ex: eye toggle fourni de l'extérieur si besoin
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const getInputStyle = () => {
    const baseStyle = [styles.input];
    if (isFocused) baseStyle.push(styles.inputFocused);
    if (error) baseStyle.push(styles.inputError);
    if (disabled) baseStyle.push(styles.inputDisabled);
    if (style) baseStyle.push(style);
    return baseStyle;
  };

  const renderIcon = () => {
    if (!icon) return null;

    return (
      <Image
        source={icon}
        resizeMode="contain"
        style={[styles.leftIcon, { width: 18, height: 18, tintColor: isFocused ? theme.colors.textSecondary : theme.colors.textSecondary }]}
      />
    );
  };

  const renderPasswordToggle = () => {
    if (!secureTextEntry) return null;
    return (
      <TouchableOpacity style={styles.rightIcon} onPress={() => setShowPassword(!showPassword)}>
        <Ionicons name={showPassword ? 'eye-off' : 'eye'} size={18} color={theme.colors.textTertiary} />
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {label && <Text style={styles.label}>{label}</Text>}
      <View style={styles.wrapper}>
        {renderIcon()}
        <TextInput
          style={getInputStyle()}
          placeholder={placeholder}
          placeholderTextColor={theme.colors.textSecondary}
          value={value}
          onChangeText={onChangeText}
          secureTextEntry={secureTextEntry && !showPassword}
          keyboardType={keyboardType}
          autoCapitalize={autoCapitalize}
          autoCorrect={autoCorrect}
          multiline={multiline}
          numberOfLines={numberOfLines}
          editable={!disabled}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          {...props}
        />
        {rightAccessory}
        {renderPasswordToggle()}
      </View>
      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
};

export const OrSeparator = ({ label = 'ou' }) => (
  <View style={styles.separatorContainer}>
    <View style={styles.separatorLine} />
    <Text style={styles.separatorText}>{label.toUpperCase()}</Text>
    <View style={styles.separatorLine} />
  </View>
);

const styles = StyleSheet.create({
  container: { marginBottom: theme.spacing.md },
  label: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.xs,
  },
  wrapper: {
    position: 'relative',
  },
  input: {
    backgroundColor: theme.colors.surfaceLight,
    //borderWidth: 1,
   // borderColor: theme.colors.surface,
    borderRadius: 20,
    paddingVertical: 14,
    paddingLeft: 44,
    paddingRight: 44,
    color: theme.colors.textPrimary,
    fontSize: theme.typography.fontSize.base,
  },
  inputFocused: {
    borderColor: theme.colors.gold,
   // borderWidth: 2,
  },
  inputError: {
    borderColor: theme.colors.error,
    borderWidth: 2,
  },
  inputDisabled: {
    opacity: 0.5,
  },
  leftIcon: {
    position: 'absolute',
    zIndex: 1,
    left: 16,
    top: 14,
  },
  rightIcon: {
    position: 'absolute',
    right: 16,
    top: 14,
  },
  errorText: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.error,
    marginTop: theme.spacing.xs,
  },
  separatorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: theme.spacing.md,
  },
  separatorLine: {
    flex: 1,
    height: 1,
    backgroundColor: theme.colors.surfaceLight,
  },
  separatorText: {
    marginHorizontal: theme.spacing.md,
    color: theme.colors.textSecondary,
    fontFamily: 'Poppins_600SemiBold',
    fontSize: theme.typography.fontSize.xs,
  },
});

export default Input;
