# Documentation API - Backend Awards

## Informations générales

- **Base URL**: `http://localhost:5000/api`
- **Port par défaut**: 5000
- **Authentification**: JWT via cookies HTTP-only
- **Format des données**: JSON

## Endpoints de santé

### Vérifier l'état du serveur
```
GET /health
```
**Réponse**: `{ "status": "OK" }`

## Authentification

### Inscription utilisateur
```
POST /api/user/register
```
**Body**:
```json
{
  "firstName": "string",
  "lastName": "string", 
  "email": "string (format email valide)",
  "phone": "string (optionnel)",
  "password": "string (min 8 caractères, 1 majuscule, 1 chiffre, 1 caractère spécial)"
}
```
**Réponse succès** (200): 
```json
{
  "success": true,
  "token": "string (JWT token pour applications mobiles)",
  "user": {
    "id": "string",
    "email": "string",
    "firstName": "string",
    "lastName": "string"
  }
}
```
**Réponse erreur** (400): `{ "error": "Email already in use" }` ou `{ "error": "Phone number already in use" }`

### Connexion utilisateur
```
POST /api/user/login
```
**Body**:
```json
{
  "email": "string",
  "password": "string"
}
```
**Réponse succès** (200): 
```json
{
  "message": "Login successful",
  "user": {
    "id": "string",
    "email": "string"
  }
}
```
**Cookie**: `token` (HTTP-only) sera automatiquement défini

### Inscription administrateur
```
POST /api/admin/register
```
**Body**:
```json
{
  "email": "string",
  "password": "string (min 8 caractères, 1 majuscule, 1 chiffre, 1 caractère spécial)"
}
```
**Réponse succès** (200): 
```json
{
  "success": true,
  "token": "string (JWT token pour applications mobiles)",
  "admin": {
    "id": "string",
    "email": "string"
  }
}
```
**Réponse erreur** (400): `{ "error": "Admin email already exists" }`

### Connexion administrateur
```
POST /api/admin/login
```
**Body**:
```json
{
  "email": "string",
  "password": "string"
}
```
**Réponse succès** (200): 
```json
{
  "success": true,
  "token": "string (JWT token pour applications mobiles)",
  "user": {
    "id": "string",
    "email": "string"
  }
}
```

### Déconnexion
```
POST /api/user/logout
POST /api/admin/logout
```
**Réponse** (200): `{ "success": true }`
**Cookie**: Le token sera automatiquement supprimé

### Vérifier le token utilisateur
```
GET /api/user/verify-token
```
**Headers**: Cookie `token` OU `Authorization: Bearer <token>` requis
**Réponse** (200): 
```json
{
  "valid": true,
  "user": {
    "id": "string",
    "email": "string",
    "firstName": "string",
    "lastName": "string",
    "phone": "string",
    "role": "user"
  }
}
```

### Profil utilisateur
```
GET /api/user/profile
```
**Headers**: Cookie `token` requis
**Réponse** (200): 
```json
{
  "user": {
    "id": "string",
    "email": "string",
    "firstName": "string",
    "lastName": "string",
    "phone": "string",
    "role": "user"
  }
}
```

### Profil administrateur
```
GET /api/admin/profile
```
**Headers**: Cookie `token` requis (admin uniquement)
**Réponse** (200): 
```json
{
  "user": {
    "id": "string",
    "email": "string",
    "role": "admin"
  }
}
```

### Vérifier le token administrateur
```
GET /api/admin/verify-token
```
**Headers**: Cookie `token` OU `Authorization: Bearer <token>` requis (admin uniquement)
**Réponse** (200): 
```json
{
  "valid": true,
  "user": {
    "id": "string",
    "email": "string",
    "role": "admin"
  }
}
```

## Gestion des catégories

### Récupérer toutes les catégories
```
GET /api/category
```
**Réponse** (200): 
```json
[
  {
    "_id": "string",
    "name": "string",
    "description": "string",
    "isDeleted": false
  }
]
```

### Créer une catégorie
```
POST /api/category
```
**Body**:
```json
{
  "name": "string (unique)",
  "description": "string (optionnel)"
}
```
**Réponse succès** (201): Catégorie créée
**Réponse erreur** (400): `{ "error": "Category name already exists" }`

### Supprimer une catégorie
```
DELETE /api/category/:id
```
**Paramètres**: `id` - ID de la catégorie
**Réponse** (200): `{ "message": "Category deleted" }`

## Gestion des stars

### Récupérer toutes les stars
```
GET /api/star
```
**Réponse** (200): 
```json
[
  {
    "_id": "string",
    "name": "string",
    "bio": "string",
    "photoUrl": "string",
    "socialLinks": ["string"],
    "category": {
      "_id": "string",
      "name": "string",
      "description": "string"
    },
    "voteCount": 0,
    "isDeleted": false
  }
]
```

### Créer une star
```
POST /api/star
```
**Body**:
```json
{
  "name": "string",
  "bio": "string (optionnel)",
  "photoUrl": "string (optionnel)",
  "socialLinks": ["string"] (optionnel),
  "category": "string (ID de la catégorie)"
}
```
**Réponse succès** (201): Star créée

### Basculer la visibilité d'une star
```
PATCH /api/star/:id/visibility
```
**Paramètres**: `id` - ID de la star
**Réponse** (200): 
```json
{
  "message": "Visibility updated",
  "visible": true
}
```

## Système de vote

### Récupérer les votes d'un utilisateur
```
GET /api/user/votes/:id
```
**Paramètres**: `id` - ID de l'utilisateur
**Headers**: Cookie `token` requis
**Réponse** (200): 
```json
[
  {
    "category": {
      "_id": "string",
      "name": "string"
    },
    "star": {
      "_id": "string", 
      "name": "string"
    }
  }
]
```

### Voter
```
POST /api/user/vote
```
**Headers**: Cookie `token` requis
**Body**:
```json
{
  "userId": "string",
  "votes": [
    {
      "category": "string (ID de la catégorie)",
      "star": "string (ID de la star)"
    }
  ]
}
```
**Réponse succès** (200): `{ "message": "Vote saved" }`
**Réponse erreur** (400): `{ "message": "Already voted" }`

## Structures de données

### Modèle User
```json
{
  "_id": "string",
  "firstName": "string",
  "lastName": "string", 
  "email": "string",
  "phone": "string",
  "voted": false,
  "votes": [
    {
      "category": "ObjectId",
      "star": "ObjectId"
    }
  ],
  "createdAt": "date"
}
```

### Modèle Admin
```json
{
  "_id": "string",
  "email": "string",
  "role": "admin",
  "isDeleted": false
}
```

### Modèle Category
```json
{
  "_id": "string",
  "name": "string",
  "description": "string",
  "isDeleted": false
}
```

### Modèle Star
```json
{
  "_id": "string",
  "name": "string",
  "bio": "string",
  "photoUrl": "string",
  "socialLinks": ["string"],
  "category": "ObjectId",
  "voteCount": 0,
  "isDeleted": false
}
```

## Codes d'erreur

- **200**: Succès
- **201**: Créé avec succès
- **400**: Erreur de validation ou données invalides
- **401**: Non authentifié (token manquant ou invalide)
- **403**: Accès interdit (droits insuffisants)
- **404**: Ressource non trouvée
- **500**: Erreur serveur interne

## Exemples d'utilisation

### Exemple de connexion avec fetch
```javascript
const login = async (email, password) => {
  const response = await fetch('http://localhost:5000/api/user/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include', // Important pour les cookies
    body: JSON.stringify({ email, password })
  });
  
  if (response.ok) {
    const data = await response.json();
    return data;
  } else {
    throw new Error('Login failed');
  }
};
```

### Exemple de requête authentifiée
```javascript
const getProfile = async () => {
  const response = await fetch('http://localhost:5000/api/user/profile', {
    method: 'GET',
    credentials: 'include' // Important pour envoyer le cookie token
  });
  
  if (response.ok) {
    return await response.json();
  } else {
    throw new Error('Not authenticated');
  }
};
```

## Authentification mobile

### Support des applications mobiles
L'API supporte deux méthodes d'authentification :

1. **Cookies HTTP-only** (recommandé pour les applications web)
2. **Headers Authorization** (recommandé pour les applications mobiles)

### Utilisation avec React Native / Mobile
```javascript
// Connexion
const login = async (email, password) => {
  const response = await fetch('http://localhost:5000/api/user/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ email, password })
  });
  
  const data = await response.json();
  
  // Stocker le token pour les requêtes suivantes
  if (data.success && data.token) {
    // Stocker le token (AsyncStorage, SecureStore, etc.)
    await AsyncStorage.setItem('authToken', data.token);
  }
  
  return data;
};

// Requête authentifiée
const getProfile = async () => {
  const token = await AsyncStorage.getItem('authToken');
  
  const response = await fetch('http://localhost:5000/api/user/profile', {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  return await response.json();
};
```

## Notes importantes

1. **Cookies**: L'authentification utilise des cookies HTTP-only pour la sécurité (web)
2. **Headers**: Support des headers Authorization pour les applications mobiles
3. **CORS**: Le serveur accepte les requêtes cross-origin avec credentials
4. **Validation**: Les mots de passe doivent respecter un format strict
5. **Vote unique**: Chaque utilisateur ne peut voter qu'une seule fois
6. **Soft delete**: Les entités supprimées sont marquées `isDeleted: true` mais pas supprimées physiquement
7. **Tokens**: Les tokens JWT expirent après 24h 