# ninja log v5
3	46	0	C:/Users/<USER>/Desktop/awards/awardsApp/android/app/.cxx/Debug/6a5k4y29/arm64-v8a/CMakeFiles/cmake.verify_globs	3e56bbd0986fc133
42	9354	7759973105425402	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	9251d18b00d5f175
21	9541	7759973107481051	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	ce43b697a2da5317
64	9861	7759973110611843	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/e18682d777342c655410643a6594f117/components/safeareacontext/EventEmitters.cpp.o	5519b45290c82a0
26	10058	7759973112465575	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	88e7e597293010dc
47	10071	7759973112689823	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	abf7e2e5b1648b72
36	10284	7759973114928071	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	cd60ff2f04a9c4fc
9	10943	7759973121254010	CMakeFiles/appmodules.dir/OnLoad.cpp.o	c077bd9ebff16ad4
31	11066	7759973122379977	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	b01ffdb8beadebd8
58	11368	7759973125462767	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7cd4a2f4725709ed36b165294390a0b8/components/safeareacontext/RNCSafeAreaViewState.cpp.o	d9e131d8030d63ef
15	11391	7759973125977517	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	c3a47c2708bc860b
99	11577	7759973127702180	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/0bbed6a10d4a6b1f8c8aab2761e1fbb3/renderer/components/safeareacontext/ShadowNodes.cpp.o	9b2678c6ac3f88f
70	11891	7759973130799138	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7fef278d7e13f2f72769aedb28204396/react/renderer/components/safeareacontext/Props.cpp.o	7bbed4acae63f69a
53	13056	7759973142089685	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/e18682d777342c655410643a6594f117/components/safeareacontext/ComponentDescriptors.cpp.o	fb8bcd604cef38eb
9363	13455	7759973146424525	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/0bbed6a10d4a6b1f8c8aab2761e1fbb3/renderer/components/safeareacontext/States.cpp.o	650ef011369a7509
9542	13682	7759973148726451	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o	3e25a4da4eaaba62
10943	14665	7759973158486352	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o	2d7fb7190d9905fd
10072	15300	7759973165032683	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o	2fd753efac43c1b2
11067	15625	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	65d46c6592a4678e
11577	16498	7759973176985558	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c64baa380ede5b93ef2bde6d021070a7/safeareacontext/safeareacontextJSI-generated.cpp.o	bcbed265d4ca7feb
9861	16640	7759973178049433	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o	16cbd65d730e85d2
11391	16680	7759973178553805	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/e9eda455ea03ba9e7d6d618514f99095/source/codegen/jni/safeareacontext-generated.cpp.o	c9be8af829846bd9
11368	16772	7759973179630523	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	f3f5ca76493495b2
10059	17186	7759973183669014	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o	f35b4d64782cde91
10285	17405	7759973185643963	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/96f9eeb2633a33651c400c9ced96984f/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o	7fc2c09dc9e72be5
11891	17482	7759973186562951	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e02703ba2d835cf3f13d1ac0dfaa68a7/react/renderer/components/rnscreens/RNSBottomTabsShadowNode.cpp.o	7dc9f6a24e7213de
17405	18001	7759973191264836	C:/Users/<USER>/Desktop/awards/awardsApp/android/app/build/intermediates/cxx/Debug/6a5k4y29/obj/arm64-v8a/libreact_codegen_safeareacontext.so	df9d4ed5cc34d152
13057	18189	7759973193786001	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/4ebd47a9cc6e91a3a47533312a70f82d/cpp/react/renderer/components/rnscreens/RNSBottomTabsState.cpp.o	a30522e36e9dfb34
13455	19358	7759973205335436	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a3ab5e2e9ddf6e46b60687ddb096364c/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	3bf8199f9f25b15e
15625	20328	7759973215226950	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a3ab5e2e9ddf6e46b60687ddb096364c/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	1b64fa585219f34c
13682	20476	7759973216681741	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/4ebd47a9cc6e91a3a47533312a70f82d/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	33cf5919bf570e84
14666	20781	7759973219692475	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a3ab5e2e9ddf6e46b60687ddb096364c/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	b749a3775e75cbc3
16640	21215	7759973224090651	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/4ebd47a9cc6e91a3a47533312a70f82d/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o	8c8c40788dbe2f11
16498	21399	7759973226007427	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a3ab5e2e9ddf6e46b60687ddb096364c/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	581e6d5bf1356bed
3	21552	7759973225782234	CMakeFiles/appmodules.dir/C_/Users/<USER>/Desktop/awards/awardsApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	7cdd0974004af449
15301	21884	7759973230568546	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/03c8a8f2e930c932133adb51cf8c5ba3/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o	aaa5acd29dc52285
18189	22019	7759973232197771	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/cdb7ebe261e8d7398af89ad59952c322/codegen/jni/react/renderer/components/rnscreens/States.cpp.o	b2008383c97ca51a
16681	22855	7759973240570105	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a3ab5e2e9ddf6e46b60687ddb096364c/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	40b2e59ee15e1291
16773	23095	7759973242686269	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	44dce0ebed708ec
17186	23106	7759973243081783	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/03c8a8f2e930c932133adb51cf8c5ba3/renderer/components/rnscreens/RNSSplitViewScreenShadowNode.cpp.o	a6d05f62384cb83d
20328	25016	7759973262016506	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/310ed2aba118eebf1bcdf8f66035cec7/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	b2815cfd1f37e916
18001	26019	7759973271671851	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/332f4af2aa21c9c433cc741f53c02c65/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o	c82e71c84198f652
21400	26039	7759973272033055	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o	6bbd0f6d5e9f9a81
21553	26565	7759973277693490	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o	914d879ee19a8cbb
19358	26623	7759973277853759	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/cdb7ebe261e8d7398af89ad59952c322/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o	565787dce0bf48f8
21215	26696	7759973278999844	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o	a782b05e56816f03
20782	26765	7759973279465429	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o	e8ca2a2b57138d36
21884	27091	7759973282884262	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o	4579c6b838b0b76b
20477	27476	7759973286734754	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/cdb7ebe261e8d7398af89ad59952c322/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	42fddb287c2fb9fa
23095	27670	7759973288658232	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o	213f3143b71c7d4c
22019	27913	7759973291166404	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o	5b3525cb8c67158d
23107	28282	7759973294857940	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o	c1865b8dbecc35ef
17482	28297	7759973294432429	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/310ed2aba118eebf1bcdf8f66035cec7/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o	430e63b95d8f628a
28297	28482	7759973296599759	C:/Users/<USER>/Desktop/awards/awardsApp/android/app/build/intermediates/cxx/Debug/6a5k4y29/obj/arm64-v8a/libreact_codegen_rnscreens.so	762685b0d3e5d465
22856	28543	7759973297200542	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o	5f077b464723f4d9
26040	29033	7759973302436991	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o	a84875508e321979
25017	29389	7759973305980503	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o	d047347438a66ee1
26565	29451	7759973306621071	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o	acfb88c78251dada
26020	29579	7759973307845721	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o	e89ebaeddad25286
26624	29758	7759973309650694	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o	398f83cfef437fce
29758	29960	7759973311484653	C:/Users/<USER>/Desktop/awards/awardsApp/android/app/build/intermediates/cxx/Debug/6a5k4y29/obj/arm64-v8a/libappmodules.so	d5e76abeedcfe671
