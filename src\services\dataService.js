import apiService from './api';

class DataService {
  constructor() {
    this.categories = [];
    this.stars = [];
    this.userVotes = [];
  }

  // Récupérer toutes les catégories
  async getCategories() {
    try {
      const categories = await apiService.getCategories();
      this.categories = categories.filter(cat => !cat.isDeleted);
      return this.categories;
    } catch (error) {
      console.error('Error fetching categories:', error);
      
      // Retourner des données de fallback si l'API n'est pas disponible
      if (error.message.includes('connecter au serveur')) {
        console.log('Using fallback categories data');
        return this.getFallbackCategories();
      }
      
      throw error;
    }
  }

  // Données de fallback pour les catégories
  getFallbackCategories() {
    return [
      {
        _id: 'fallback-1',
        name: 'Meilleur Acteur',
        description: 'Performance masculine exceptionnelle',
        isDeleted: false
      },
      {
        _id: 'fallback-2',
        name: '<PERSON><PERSON>ure Actrice',
        description: 'Performance féminine exceptionnelle',
        isDeleted: false
      },
      {
        _id: 'fallback-3',
        name: 'Meilleur Film',
        description: 'Film de l\'année',
        isDeleted: false
      }
    ];
  }

  // Récupérer toutes les stars
  async getStars() {
    try {
      const stars = await apiService.getStars();
      this.stars = stars.filter(star => !star.isDeleted);
      return this.stars;
    } catch (error) {
      console.error('Error fetching stars:', error);
      
      // Retourner des données de fallback si l'API n'est pas disponible
      if (error.message.includes('connecter au serveur')) {
        console.log('Using fallback stars data');
        return this.getFallbackStars();
      }
      
      throw error;
    }
  }

  // Données de fallback pour les stars
  getFallbackStars() {
    return [
      {
        _id: 'star-1',
        name: 'Brad Pitt',
        bio: 'Acteur américain renommé',
        photoUrl: '',
        socialLinks: [],
        category: { _id: 'fallback-1', name: 'Meilleur Acteur', description: 'Performance masculine exceptionnelle' },
        voteCount: 0,
        isDeleted: false
      },
      {
        _id: 'star-2',
        name: 'Meryl Streep',
        bio: 'Actrice américaine légendaire',
        photoUrl: '',
        socialLinks: [],
        category: { _id: 'fallback-2', name: 'Meilleure Actrice', description: 'Performance féminine exceptionnelle' },
        voteCount: 0,
        isDeleted: false
      },
      {
        _id: 'star-3',
        name: 'Inception',
        bio: 'Film de science-fiction révolutionnaire',
        photoUrl: '',
        socialLinks: [],
        category: { _id: 'fallback-3', name: 'Meilleur Film', description: 'Film de l\'année' },
        voteCount: 0,
        isDeleted: false
      }
    ];
  }

  // Récupérer les stars par catégorie
  async getStarsByCategory(categoryId) {
    try {
      const stars = await this.getStars();
      return stars.filter(star => star.category._id === categoryId);
    } catch (error) {
      console.error('Error fetching stars by category:', error);
      throw error;
    }
  }

  // Récupérer les votes d'un utilisateur
  async getUserVotes(userId) {
    try {
      const votes = await apiService.getUserVotes(userId);
      this.userVotes = votes;
      return votes;
    } catch (error) {
      console.error('Error fetching user votes:', error);
      
      // Si erreur de token, retourner un tableau vide
      if (error.message.includes('Token') || error.message.includes('401')) {
        console.log('Token error, returning empty votes');
        this.userVotes = [];
        return [];
      }
      
      throw error;
    }
  }

  // Soumettre un vote
  async submitVote(userId, votes) {
    try {
      const voteData = {
        userId,
        votes: votes.map(vote => ({
          category: vote.category,
          star: vote.star,
        })),
      };

      const response = await apiService.submitVote(voteData);
      
      // Mettre à jour les votes locaux avec le format de l'API
      this.userVotes = votes.map(vote => ({
        category: { _id: vote.category, name: '' }, // L'API retourne ce format
        star: { _id: vote.star, name: '' }
      }));
      
      return response;
    } catch (error) {
      console.error('Error submitting vote:', error);
      throw error;
    }
  }

  // Vérifier si l'utilisateur a déjà voté
  hasUserVoted() {
    return this.userVotes.length > 0;
  }

  // Obtenir le vote de l'utilisateur pour une catégorie
  getUserVoteForCategory(categoryId) {
    const vote = this.userVotes.find(v => v.category._id === categoryId);
    return vote ? vote.star._id : null;
  }

  // Formater les données pour l'affichage
  formatCategoryForDisplay(category) {
    const stars = this.stars.filter(star => 
      star.category._id === category._id && !star.isDeleted
    );

    return {
      id: category._id,
      name: category.name,
      description: category.description,
      candidates: stars.map(star => ({
        id: star._id,
        name: star.name,
        description: star.bio || 'Aucune description disponible',
        photoUrl: star.photoUrl,
        socialLinks: star.socialLinks || [],
        voteCount: star.voteCount || 0,
      })),
      color: this.getCategoryColor(category.name),
      icon: this.getCategoryIcon(category.name),
    };
  }

  // Couleurs par défaut pour les catégories
  getCategoryColor(categoryName) {
    const colors = {
      'Meilleur Acteur': '#FF6B6B',
      'Meilleure Actrice': '#4ECDC4',
      'Meilleur Film': '#45B7D1',
      'Meilleure Réalisation': '#96CEB4',
      'Meilleur Scénario': '#FFEAA7',
      'Meilleure Musique': '#DDA0DD',
      'Meilleurs Effets Visuels': '#98D8C8',
      'Meilleur Montage': '#F7DC6F',
    };

    return colors[categoryName] || '#FF6B6B';
  }

  // Icônes par défaut pour les catégories
  getCategoryIcon(categoryName) {
    const icons = {
      'Meilleur Acteur': 'person',
      'Meilleure Actrice': 'person',
      'Meilleur Film': 'film',
      'Meilleure Réalisation': 'videocam',
      'Meilleur Scénario': 'document-text',
      'Meilleure Musique': 'musical-notes',
      'Meilleurs Effets Visuels': 'eye',
      'Meilleur Montage': 'cut',
    };

    return icons[categoryName] || 'star';
  }

  // Obtenir toutes les catégories formatées
  async getFormattedCategories() {
    await this.getCategories();
    await this.getStars();
    
    return this.categories.map(category => 
      this.formatCategoryForDisplay(category)
    );
  }

  // Vérifier la santé du serveur
  async checkServerHealth() {
    try {
      const health = await apiService.checkHealth();
      return health.status === 'OK';
    } catch (error) {
      console.error('Server health check failed:', error);
      return false;
    }
  }
}

// Instance singleton
const dataService = new DataService();

export default dataService; 