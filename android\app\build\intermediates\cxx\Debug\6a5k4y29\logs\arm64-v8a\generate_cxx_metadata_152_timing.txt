# C/C++ build system timings
generate_cxx_metadata
  [gap of 51ms]
  create-invalidation-state 25ms
  generate-prefab-packages
    [gap of 25ms]
    exec-prefab 2090ms
    [gap of 19ms]
  generate-prefab-packages completed in 2134ms
  execute-generate-process
    exec-configure 3021ms
    [gap of 72ms]
  execute-generate-process completed in 3097ms
  [gap of 53ms]
generate_cxx_metadata completed in 5372ms

