{"buildFiles": ["C:\\Users\\<USER>\\Desktop\\awards\\awardsApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\awards\\awardsApp\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\awards\\awardsApp\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\awards\\awardsApp\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\awards\\awardsApp\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\awards\\awardsApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\awards\\awardsApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\awards\\awardsApp\\android\\app\\.cxx\\Debug\\6a5k4y29\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\awards\\awardsApp\\android\\app\\.cxx\\Debug\\6a5k4y29\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"react_codegen_RNVectorIconsSpec::@479809fae146501fd34d": {"artifactName": "react_codegen_RNVectorIconsSpec", "abi": "x86_64", "runtimeFiles": []}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"artifactName": "react_codegen_safeareacontext", "abi": "x86_64", "output": "C:\\Users\\<USER>\\Desktop\\awards\\awardsApp\\android\\app\\build\\intermediates\\cxx\\Debug\\6a5k4y29\\obj\\x86_64\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\492d12ddde8d0414cdeaa8d1c9a89b37\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ecf58a1d5a3b5cc4faabc13e873181f2\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ecf58a1d5a3b5cc4faabc13e873181f2\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so"]}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"artifactName": "react_codegen_rngesturehandler_codegen", "abi": "x86_64", "runtimeFiles": []}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"artifactName": "react_codegen_rnscreens", "abi": "x86_64", "output": "C:\\Users\\<USER>\\Desktop\\awards\\awardsApp\\android\\app\\build\\intermediates\\cxx\\Debug\\6a5k4y29\\obj\\x86_64\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ecf58a1d5a3b5cc4faabc13e873181f2\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ecf58a1d5a3b5cc4faabc13e873181f2\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\492d12ddde8d0414cdeaa8d1c9a89b37\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so"]}, "appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "x86_64", "output": "C:\\Users\\<USER>\\Desktop\\awards\\awardsApp\\android\\app\\build\\intermediates\\cxx\\Debug\\6a5k4y29\\obj\\x86_64\\libappmodules.so", "runtimeFiles": ["C:\\Users\\<USER>\\Desktop\\awards\\awardsApp\\android\\app\\build\\intermediates\\cxx\\Debug\\6a5k4y29\\obj\\x86_64\\libreact_codegen_safeareacontext.so", "C:\\Users\\<USER>\\Desktop\\awards\\awardsApp\\android\\app\\build\\intermediates\\cxx\\Debug\\6a5k4y29\\obj\\x86_64\\libreact_codegen_rnscreens.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\492d12ddde8d0414cdeaa8d1c9a89b37\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ecf58a1d5a3b5cc4faabc13e873181f2\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ecf58a1d5a3b5cc4faabc13e873181f2\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so"]}, "react_codegen_RNEdgeToEdge::@668218a48b60eb28aa9e": {"artifactName": "react_codegen_RNEdgeToEdge", "abi": "x86_64", "runtimeFiles": []}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"artifactName": "react_codegen_rnasyncstorage", "abi": "x86_64", "runtimeFiles": []}}}