# Checklist de vérification Backend

## 🔍 **Vérifications à effectuer dans votre backend**

### **1. Configuration de base**

- [ ] **Port 5000** : Le serveur écoute sur le port 5000
- [ ] **CORS configuré** : `credentials: true` activé
- [ ] **<PERSON>ie parser** : `cookie-parser` installé et configuré
- [ ] **JWT secret** : Variable d'environnement `JWT_SECRET` définie

### **2. Endpoints de santé**

- [ ] **GET /health** : Retourne `{ "status": "OK" }`
- [ ] **Accessible sans authentification**

### **3. Authentification**

#### **POST /api/user/login**
- [ ] **Validation des identifiants** : email et mot de passe
- [ ] **Création du token JWT** : `jwt.sign()` avec expiration 24h
- [ ] **Cookie HTTP-only** : `res.cookie('token', token, { httpOnly: true })`
- [ ] **Réponse** : `{ "message": "Login successful", "user": { "id", "email" } }`

#### **GET /api/user/profile**
- [ ] **Middleware d'authentification** : Vérification du token
- [ ] **Cookie token** : Lecture depuis `req.cookies.token`
- [ ] **Header Authorization** : Support de `Authorization: Bearer <token>`
- [ ] **Réponse** : `{ "user": { "id", "email", "firstName", "lastName", "phone", "role" } }`

### **4. Gestion des catégories**

#### **GET /api/category**
- [ ] **Accessible sans authentification**
- [ ] **Filtrage** : `isDeleted: false`
- [ ] **Réponse** : `[{ "_id", "name", "description", "isDeleted" }]`

### **5. Gestion des stars**

#### **GET /api/star**
- [ ] **Accessible sans authentification**
- [ ] **Filtrage** : `isDeleted: false`
- [ ] **Population** : `category` avec `{ "_id", "name", "description" }`
- [ ] **Réponse** : `[{ "_id", "name", "bio", "photoUrl", "socialLinks", "category", "voteCount", "isDeleted" }]`

### **6. Système de vote**

#### **GET /api/user/votes/:id**
- [ ] **Authentification requise**
- [ ] **Paramètre** : `id` de l'utilisateur
- [ ] **Réponse** : `[{ "category": { "_id", "name" }, "star": { "_id", "name" } }]`

#### **POST /api/user/vote**
- [ ] **Authentification requise**
- [ ] **Body** : `{ "userId", "votes": [{ "category", "star" }] }`
- [ ] **Validation** : Un vote par utilisateur
- [ ] **Réponse succès** : `{ "message": "Vote saved" }`
- [ ] **Réponse erreur** : `{ "message": "Already voted" }`

### **7. Configuration CORS**

```javascript
const cors = require('cors');

app.use(cors({
  origin: true, // Ou vos domaines spécifiques
  credentials: true // CRUCIAL pour les cookies
}));
```

### **8. Middleware d'authentification**

```javascript
const authenticateToken = (req, res, next) => {
  // Vérifier le cookie
  const token = req.cookies.token;
  
  // Vérifier le header Authorization
  const authHeader = req.headers['authorization'];
  const bearerToken = authHeader && authHeader.split(' ')[1];
  
  const finalToken = token || bearerToken;
  
  if (!finalToken) {
    return res.status(401).json({ error: 'Token missing' });
  }

  jwt.verify(finalToken, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      console.log('Token verification failed:', err.message);
      return res.status(401).json({ error: 'Token invalid' });
    }
    req.user = user;
    next();
  });
};
```

### **9. Route de login**

```javascript
app.post('/api/user/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    // Validation
    if (!email || !password) {
      return res.status(400).json({ error: 'Email and password required' });
    }
    
    // Recherche utilisateur
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }
    
    // Vérification mot de passe
    const isValidPassword = await user.comparePassword(password);
    if (!isValidPassword) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }
    
    // Création du token
    const token = jwt.sign(
      { userId: user._id },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );
    
    // Définition du cookie
    res.cookie('token', token, {
      httpOnly: true,
      secure: false, // false pour le développement
      sameSite: 'lax',
      maxAge: 24 * 60 * 60 * 1000 // 24 heures
    });
    
    // Réponse
    res.json({
      message: 'Login successful',
      user: {
        id: user._id,
        email: user.email
      }
    });
    
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});
```

### **10. Tests à effectuer**

#### **Avec curl :**
```bash
# Test de santé
curl http://localhost:5000/health

# Test de connexion
curl -X POST http://localhost:5000/api/user/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Test123!"}' \
  -c cookies.txt

# Test du profil avec cookie
curl http://localhost:5000/api/user/profile \
  -b cookies.txt

# Test des catégories
curl http://localhost:5000/api/category

# Test des stars
curl http://localhost:5000/api/star
```

#### **Avec Postman :**
1. Créez une collection pour tester tous les endpoints
2. Configurez les cookies automatiquement
3. Testez chaque endpoint dans l'ordre

### **11. Problèmes courants**

- **"Token missing"** : Cookie non défini ou CORS mal configuré
- **"Token invalid"** : JWT_SECRET incorrect ou token expiré
- **"CORS error"** : `credentials: true` manquant
- **"Network error"** : Serveur non démarré ou port incorrect

### **12. Logs de débogage**

Ajoutez des logs dans votre backend :

```javascript
// Dans le middleware d'authentification
console.log('Token from cookie:', req.cookies.token);
console.log('Token from header:', req.headers['authorization']);

// Dans la route de login
console.log('Login attempt for:', email);
console.log('Token created:', token);
```

### **13. Vérification finale**

Après avoir corrigé le backend :

1. **Redémarrez le serveur**
2. **Testez avec curl** ou Postman
3. **Utilisez le composant BackendTest** dans l'app
4. **Vérifiez les logs** du serveur
5. **Testez la connexion** depuis l'app

Si tous les tests passent, l'authentification devrait fonctionner correctement ! 🎉 