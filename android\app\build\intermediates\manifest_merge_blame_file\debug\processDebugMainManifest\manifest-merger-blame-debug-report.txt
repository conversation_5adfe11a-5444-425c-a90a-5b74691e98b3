1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.sloumache.awardsApp"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:4:3-75
11-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:4:20-73
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:2:3-64
12-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:2:20-62
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:3:3-77
13-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:3:20-75
14    <uses-permission android:name="android.permission.VIBRATE" />
14-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:5:3-63
14-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:5:20-61
15    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
15-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:6:3-78
15-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:6:20-76
16
17    <queries>
17-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:7:3-13:13
18        <intent>
18-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:8:5-12:14
19            <action android:name="android.intent.action.VIEW" />
19-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:9:7-58
19-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:9:15-56
20
21            <category android:name="android.intent.category.BROWSABLE" />
21-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:10:7-67
21-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:10:17-65
22
23            <data android:scheme="https" />
23-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:11:7-37
23-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:11:13-35
24        </intent>
25        <!-- Query open documents -->
26        <intent>
26-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:15:9-17:18
27            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
27-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:13-79
27-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:21-76
28        </intent>
29    </queries>
30
31    <permission
31-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad3fede12f460d3ded14c5028a9d47f8\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
32        android:name="com.sloumache.awardsApp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
32-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad3fede12f460d3ded14c5028a9d47f8\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
33        android:protectionLevel="signature" />
33-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad3fede12f460d3ded14c5028a9d47f8\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
34
35    <uses-permission android:name="com.sloumache.awardsApp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
35-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad3fede12f460d3ded14c5028a9d47f8\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
35-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad3fede12f460d3ded14c5028a9d47f8\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
36
37    <application
37-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:14:3-24:17
38        android:name="com.sloumache.awardsApp.MainApplication"
38-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:14:16-47
39        android:allowBackup="true"
39-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:14:162-188
40        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
40-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad3fede12f460d3ded14c5028a9d47f8\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
41        android:debuggable="true"
42        android:extractNativeLibs="false"
43        android:icon="@mipmap/ic_launcher"
43-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:14:81-115
44        android:label="@string/app_name"
44-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:14:48-80
45        android:roundIcon="@mipmap/ic_launcher_round"
45-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:14:116-161
46        android:supportsRtl="true"
46-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:14:221-247
47        android:theme="@style/AppTheme"
47-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:14:189-220
48        android:usesCleartextTraffic="true" >
48-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\debug\AndroidManifest.xml:6:18-53
49        <meta-data
49-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:15:5-83
50            android:name="expo.modules.updates.ENABLED"
50-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:15:16-59
51            android:value="false" />
51-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:15:60-81
52        <meta-data
52-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:16:5-105
53            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
53-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:16:16-80
54            android:value="ALWAYS" />
54-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:16:81-103
55        <meta-data
55-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:17:5-99
56            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
56-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:17:16-79
57            android:value="0" />
57-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:17:80-97
58
59        <activity
59-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:18:5-23:16
60            android:name="com.sloumache.awardsApp.MainActivity"
60-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:18:15-43
61            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
61-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:18:44-134
62            android:exported="true"
62-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:18:256-279
63            android:launchMode="singleTask"
63-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:18:135-166
64            android:screenOrientation="portrait"
64-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:18:280-316
65            android:theme="@style/Theme.App.SplashScreen"
65-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:18:210-255
66            android:windowSoftInputMode="adjustResize" >
66-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:18:167-209
67            <intent-filter>
67-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:19:7-22:23
68                <action android:name="android.intent.action.MAIN" />
68-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:20:9-60
68-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:20:17-58
69
70                <category android:name="android.intent.category.LAUNCHER" />
70-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:21:9-68
70-->C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\AndroidManifest.xml:21:19-66
71            </intent-filter>
72        </activity>
73
74        <meta-data
74-->[:expo-modules-core] C:\Users\<USER>\Desktop\awards\awardsApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
75            android:name="org.unimodules.core.AppLoader#react-native-headless"
75-->[:expo-modules-core] C:\Users\<USER>\Desktop\awards\awardsApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
76            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
76-->[:expo-modules-core] C:\Users\<USER>\Desktop\awards\awardsApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
77        <meta-data
77-->[:expo-modules-core] C:\Users\<USER>\Desktop\awards\awardsApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
78            android:name="com.facebook.soloader.enabled"
78-->[:expo-modules-core] C:\Users\<USER>\Desktop\awards\awardsApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
79            android:value="true" />
79-->[:expo-modules-core] C:\Users\<USER>\Desktop\awards\awardsApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
80
81        <activity
81-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecf58a1d5a3b5cc4faabc13e873181f2\transformed\react-android-0.79.5-debug\AndroidManifest.xml:19:9-21:40
82            android:name="com.facebook.react.devsupport.DevSettingsActivity"
82-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecf58a1d5a3b5cc4faabc13e873181f2\transformed\react-android-0.79.5-debug\AndroidManifest.xml:20:13-77
83            android:exported="false" />
83-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecf58a1d5a3b5cc4faabc13e873181f2\transformed\react-android-0.79.5-debug\AndroidManifest.xml:21:13-37
84
85        <provider
85-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:21:9-30:20
86            android:name="expo.modules.filesystem.FileSystemFileProvider"
86-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:22:13-74
87            android:authorities="com.sloumache.awardsApp.FileSystemFileProvider"
87-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:23:13-74
88            android:exported="false"
88-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:24:13-37
89            android:grantUriPermissions="true" >
89-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:25:13-47
90            <meta-data
90-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:27:13-29:70
91                android:name="android.support.FILE_PROVIDER_PATHS"
91-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:28:17-67
92                android:resource="@xml/file_system_provider_paths" />
92-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:29:17-67
93        </provider>
94        <provider
94-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5895da00ec1caed18a5ab5e37ba29377\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
95            android:name="androidx.startup.InitializationProvider"
95-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5895da00ec1caed18a5ab5e37ba29377\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
96            android:authorities="com.sloumache.awardsApp.androidx-startup"
96-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5895da00ec1caed18a5ab5e37ba29377\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
97            android:exported="false" >
97-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5895da00ec1caed18a5ab5e37ba29377\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
98            <meta-data
98-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5895da00ec1caed18a5ab5e37ba29377\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
99                android:name="androidx.emoji2.text.EmojiCompatInitializer"
99-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5895da00ec1caed18a5ab5e37ba29377\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
100                android:value="androidx.startup" />
100-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5895da00ec1caed18a5ab5e37ba29377\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
101            <meta-data
101-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a60d671dd3aa291ecde7f9adf86d9123\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
102                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
102-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a60d671dd3aa291ecde7f9adf86d9123\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
103                android:value="androidx.startup" />
103-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a60d671dd3aa291ecde7f9adf86d9123\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
104            <meta-data
104-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
105                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
106                android:value="androidx.startup" />
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
107        </provider>
108
109        <receiver
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
110            android:name="androidx.profileinstaller.ProfileInstallReceiver"
110-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
111            android:directBootAware="false"
111-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
112            android:enabled="true"
112-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
113            android:exported="true"
113-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
114            android:permission="android.permission.DUMP" >
114-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
115            <intent-filter>
115-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
116                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
117            </intent-filter>
118            <intent-filter>
118-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
119                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
120            </intent-filter>
121            <intent-filter>
121-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
122                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
123            </intent-filter>
124            <intent-filter>
124-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
125                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
125-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
125-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
126            </intent-filter>
127        </receiver>
128    </application>
129
130</manifest>
