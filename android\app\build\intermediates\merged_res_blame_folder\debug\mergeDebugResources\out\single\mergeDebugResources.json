[{"merged": "com.sloumache.awardsApp.app-debug-46:/mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "com.sloumache.awardsApp.app-main-48:/mipmap-xhdpi/ic_launcher_foreground.webp"}, {"merged": "com.sloumache.awardsApp.app-debug-46:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.sloumache.awardsApp.app-main-48:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.sloumache.awardsApp.app-debug-46:/drawable_rn_edit_text_material.xml.flat", "source": "com.sloumache.awardsApp.app-main-48:/drawable/rn_edit_text_material.xml"}, {"merged": "com.sloumache.awardsApp.app-debug-46:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.sloumache.awardsApp.app-main-48:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.sloumache.awardsApp.app-debug-46:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.sloumache.awardsApp.app-main-48:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.sloumache.awardsApp.app-debug-46:/mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "com.sloumache.awardsApp.app-main-48:/mipmap-mdpi/ic_launcher_foreground.webp"}, {"merged": "com.sloumache.awardsApp.app-debug-46:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.sloumache.awardsApp.app-main-48:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.sloumache.awardsApp.app-debug-46:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.sloumache.awardsApp.app-main-48:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.sloumache.awardsApp.app-debug-46:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.sloumache.awardsApp.app-main-48:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.sloumache.awardsApp.app-debug-46:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.sloumache.awardsApp.app-main-48:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.sloumache.awardsApp.app-debug-46:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.sloumache.awardsApp.app-main-48:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.sloumache.awardsApp.app-debug-46:/mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "com.sloumache.awardsApp.app-main-48:/mipmap-xxhdpi/ic_launcher_foreground.webp"}, {"merged": "com.sloumache.awardsApp.app-debug-46:/drawable_ic_launcher_background.xml.flat", "source": "com.sloumache.awardsApp.app-main-48:/drawable/ic_launcher_background.xml"}, {"merged": "com.sloumache.awardsApp.app-debug-46:/drawable-xhdpi_splashscreen_logo.png.flat", "source": "com.sloumache.awardsApp.app-main-48:/drawable-xhdpi/splashscreen_logo.png"}, {"merged": "com.sloumache.awardsApp.app-debug-46:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.sloumache.awardsApp.app-main-48:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.sloumache.awardsApp.app-debug-46:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.sloumache.awardsApp.app-main-48:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.sloumache.awardsApp.app-debug-46:/drawable-xxxhdpi_splashscreen_logo.png.flat", "source": "com.sloumache.awardsApp.app-main-48:/drawable-xxxhdpi/splashscreen_logo.png"}, {"merged": "com.sloumache.awardsApp.app-debug-46:/mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "com.sloumache.awardsApp.app-main-48:/mipmap-xxxhdpi/ic_launcher_foreground.webp"}, {"merged": "com.sloumache.awardsApp.app-debug-46:/mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "com.sloumache.awardsApp.app-main-48:/mipmap-hdpi/ic_launcher_foreground.webp"}, {"merged": "com.sloumache.awardsApp.app-debug-46:/drawable-xxhdpi_splashscreen_logo.png.flat", "source": "com.sloumache.awardsApp.app-main-48:/drawable-xxhdpi/splashscreen_logo.png"}, {"merged": "com.sloumache.awardsApp.app-debug-46:/drawable-hdpi_splashscreen_logo.png.flat", "source": "com.sloumache.awardsApp.app-main-48:/drawable-hdpi/splashscreen_logo.png"}, {"merged": "com.sloumache.awardsApp.app-debug-46:/drawable-mdpi_splashscreen_logo.png.flat", "source": "com.sloumache.awardsApp.app-main-48:/drawable-mdpi/splashscreen_logo.png"}, {"merged": "com.sloumache.awardsApp.app-debug-46:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.sloumache.awardsApp.app-main-48:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.sloumache.awardsApp.app-debug-46:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.sloumache.awardsApp.app-main-48:/mipmap-hdpi/ic_launcher.webp"}]