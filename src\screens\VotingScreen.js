import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import dataService from '../services/dataService';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import GradientView from '../components/ui/GradientView';
import theme from '../theme/theme';

const VotingScreen = ({ route, navigation }) => {
  const { user } = useAuth();
  const { category, currentVote } = route.params;
  const [selectedCandidate, setSelectedCandidate] = useState(currentVote || null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Mettre à jour le titre de la navigation
    navigation.setOptions({
      title: `Vote - ${category.name}`,
    });
  }, [category, navigation]);

  const handleVote = async () => {
    if (!selectedCandidate) {
      Alert.alert('Erreur', 'Veuillez sélectionner un candidat');
      return;
    }

    if (!user) {
      Alert.alert('Erreur', 'Vous devez être connecté pour voter');
      return;
    }

    setLoading(true);

    try {
      // Créer le vote pour l'API
      const vote = {
        category: category.id,
        star: selectedCandidate,
      };

      // Soumettre le vote via l'API
      await dataService.submitVote(user.id, [vote]);

      const candidateName = category.candidates.find(c => c.id === selectedCandidate)?.name;

      Alert.alert(
        'Vote enregistré',
        `Votre vote pour "${candidateName}" dans la catégorie ${category.name} a été enregistré.`,
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Erreur', error.message || 'Impossible d\'enregistrer votre vote. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };

  const renderCandidate = ({ item: candidate }) => {
    const isSelected = selectedCandidate === candidate.id;

    return (
      <TouchableOpacity
        style={[styles.candidateCard, isSelected && styles.candidateCardSelected]}
        onPress={() => setSelectedCandidate(candidate.id)}
        activeOpacity={0.8}
      >
        <View style={styles.candidateHeader}>
          <View style={[styles.candidateAvatar, { backgroundColor: category.color }]}>
            <Text style={styles.candidateInitial}>
              {candidate.name.charAt(0).toUpperCase()}
            </Text>
          </View>
          <View style={styles.candidateInfo}>
            <Text style={styles.candidateName}>{candidate.name}</Text>
            <Text style={styles.candidateDescription}>{candidate.description}</Text>
          </View>
          <View style={styles.selectionIndicator}>
            {isSelected ? (
              <Ionicons name="radio-button-on" size={24} color={category.color} />
            ) : (
              <Ionicons name="radio-button-off" size={24} color="#b0b0b0" />
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <GradientView gradient="premium" style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <ScrollView style={styles.scrollView}>
          {/* Header de la catégorie */}
          <Card variant="glass" style={styles.categoryHeader}>
            <View style={[styles.categoryIcon, { backgroundColor: category.color }]}>
              <Ionicons name={category.icon} size={30} color="#fff" />
            </View>
            <View style={styles.categoryInfo}>
              <Text style={styles.categoryName}>{category.name}</Text>
              <Text style={styles.categoryDescription}>
                Sélectionnez votre candidat préféré dans cette catégorie
              </Text>
            </View>
          </Card>

        {/* Instructions */}
        <Card variant="elevated" style={styles.instructionsSection}>
          <Text style={styles.instructionsTitle}>Instructions</Text>
          <Text style={styles.instructionsText}>
            • Vous ne pouvez voter qu'une seule fois par catégorie{'\n'}
            • Votre vote peut être modifié jusqu'à l'envoi final{'\n'}
            • Tous les votes doivent être complétés avant l'envoi
          </Text>
        </Card>

        {/* Liste des candidats */}
        <Card variant="primary" style={styles.candidatesSection}>
          <Text style={styles.sectionTitle}>
            Candidats ({category.candidates.length})
          </Text>
          <FlatList
            data={category.candidates}
            renderItem={renderCandidate}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        </Card>

        {/* Bouton de vote */}
        <Card variant="accent" style={styles.voteSection}>
          <Button
            title={currentVote ? 'Modifier mon vote' : 'Confirmer mon vote'}
            onPress={handleVote}
            variant="primary"
            size="large"
            icon="checkmark"
            loading={loading}
            disabled={!selectedCandidate}
            style={styles.voteButton}
          />

          {selectedCandidate && (
            <View style={styles.selectedInfo}>
              <Text style={styles.selectedLabel}>Candidat sélectionné :</Text>
              <Text style={styles.selectedName}>
                {category.candidates.find(c => c.id === selectedCandidate)?.name}
              </Text>
            </View>
          )}
        </Card>
      </ScrollView>
      </SafeAreaView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    padding: theme.spacing.md,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  categoryIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryInfo: {
    flex: 1,
    marginLeft: theme.spacing.md,
  },
  categoryName: {
    color: theme.colors.textPrimary,
    fontSize: theme.typography.fontSize['2xl'],
    fontWeight: theme.typography.fontWeight.bold,
  },
  categoryDescription: {
    color: theme.colors.textSecondary,
    fontSize: theme.typography.fontSize.base,
    marginTop: theme.spacing.xs,
  },
  instructionsSection: {
    marginBottom: theme.spacing.lg,
  },
  instructionsTitle: {
    color: theme.colors.textPrimary,
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.bold,
    marginBottom: theme.spacing.md,
  },
  instructionsText: {
    color: theme.colors.textSecondary,
    fontSize: theme.typography.fontSize.base,
    lineHeight: theme.typography.lineHeight.relaxed,
  },
  candidatesSection: {
    marginBottom: theme.spacing.lg,
  },
  sectionTitle: {
    color: theme.colors.textPrimary,
    fontSize: theme.typography.fontSize.xl,
    fontWeight: theme.typography.fontWeight.bold,
    marginBottom: theme.spacing.md,
  },
  candidateCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.sm,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  candidateCardSelected: {
    borderColor: theme.colors.gold,
    backgroundColor: theme.colors.surfaceLight,
  },
  candidateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  candidateAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  candidateInitial: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  candidateInfo: {
    flex: 1,
    marginLeft: theme.spacing.md,
  },
  candidateName: {
    color: theme.colors.textPrimary,
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.bold,
  },
  candidateDescription: {
    color: theme.colors.textSecondary,
    fontSize: theme.typography.fontSize.base,
    marginTop: theme.spacing.xs,
  },
  selectionIndicator: {
    marginLeft: theme.spacing.sm,
  },
  voteSection: {
    alignItems: 'center',
  },
  voteButton: {
    width: '100%',
  },
  selectedInfo: {
    marginTop: theme.spacing.md,
    padding: theme.spacing.md,
    backgroundColor: theme.colors.surfaceLight,
    borderRadius: theme.borderRadius.md,
    width: '100%',
    alignItems: 'center',
  },
  selectedLabel: {
    color: theme.colors.textSecondary,
    fontSize: theme.typography.fontSize.sm,
  },
  selectedName: {
    color: theme.colors.gold,
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.bold,
    marginTop: theme.spacing.xs,
  },
});

export default VotingScreen;
