import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import apiService from '../services/api';
import config from '../config/config';

const ConnectionTest = () => {
  const [testing, setTesting] = useState(false);
  const [status, setStatus] = useState('idle');

  const testConnection = async () => {
    setTesting(true);
    setStatus('testing');

    try {
      // Test de santé du serveur
      const health = await apiService.checkHealth();
      console.log('Health check response:', health);
      
      if (health.status === 'OK') {
        setStatus('success');
        Alert.alert(
          'Connexion réussie !',
          `Serveur accessible sur ${config.API_BASE_URL}`,
          [{ text: 'OK' }]
        );
      } else {
        setStatus('error');
        Alert.alert('Erreur', 'Le serveur répond mais avec une erreur');
      }
    } catch (error) {
      console.error('Connection test failed:', error);
      setStatus('error');
      
      let message = 'Erreur de connexion inconnue';
      
      if (error.message.includes('Network request failed')) {
        message = `Impossible de se connecter à ${config.API_BASE_URL}\n\nVérifiez que :\n• Le backend est démarré sur le port 5000\n• L'URL est correcte pour votre plateforme\n• Aucun firewall ne bloque la connexion`;
      } else if (error.message.includes('timeout')) {
        message = 'Délai d\'attente dépassé. Le serveur ne répond pas.';
      } else if (error.message.includes('CORS')) {
        message = 'Erreur CORS. Vérifiez la configuration du serveur.';
      }
      
      Alert.alert('Test de connexion échoué', message, [
        { text: 'OK' },
        { 
          text: 'Voir la config', 
          onPress: () => {
            Alert.alert(
              'Configuration actuelle',
              `URL API: ${config.API_BASE_URL}\nEnvironnement: ${config.environment}`,
              [{ text: 'OK' }]
            );
          }
        }
      ]);
    } finally {
      setTesting(false);
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'success':
        return <Ionicons name="checkmark-circle" size={24} color="#4CAF50" />;
      case 'error':
        return <Ionicons name="close-circle" size={24} color="#f44336" />;
      case 'testing':
        return <Ionicons name="sync" size={24} color="#2196F3" />;
      default:
        return <Ionicons name="help-circle" size={24} color="#FF9800" />;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'success':
        return 'Connexion OK';
      case 'error':
        return 'Erreur de connexion';
      case 'testing':
        return 'Test en cours...';
      default:
        return 'Non testé';
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Test de connexion API</Text>
      
      <View style={styles.statusContainer}>
        {getStatusIcon()}
        <Text style={styles.statusText}>{getStatusText()}</Text>
      </View>
      
      <Text style={styles.urlText}>URL: {config.API_BASE_URL}</Text>
      
      <TouchableOpacity
        style={[styles.testButton, testing && styles.testButtonDisabled]}
        onPress={testConnection}
        disabled={testing}
      >
        <Ionicons name="wifi" size={20} color="#fff" />
        <Text style={styles.testButtonText}>
          {testing ? 'Test en cours...' : 'Tester la connexion'}
        </Text>
      </TouchableOpacity>
      
      <Text style={styles.helpText}>
        Utilisez ce test pour vérifier que votre backend est accessible depuis l'application.
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#1a1a2e',
    padding: 20,
    borderRadius: 12,
    margin: 20,
  },
  title: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  statusText: {
    color: '#fff',
    fontSize: 16,
    marginLeft: 10,
  },
  urlText: {
    color: '#b0b0b0',
    fontSize: 14,
    marginBottom: 20,
    fontFamily: 'monospace',
  },
  testButton: {
    backgroundColor: '#2196F3',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginBottom: 15,
  },
  testButtonDisabled: {
    backgroundColor: '#666',
  },
  testButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  helpText: {
    color: '#b0b0b0',
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 16,
  },
});

export default ConnectionTest; 