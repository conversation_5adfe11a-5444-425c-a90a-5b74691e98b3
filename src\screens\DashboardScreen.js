import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import dataService from '../services/dataService';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import GradientView from '../components/ui/GradientView';
import theme from '../theme/theme';

// Données de démonstration des catégories et candidats (fallback)
const FALLBACK_CATEGORIES = [
  {
    id: 'artists',
    name: 'Artistes',
    icon: 'musical-notes',
    color: '#ff6b6b',
    candidates: [
      { id: 'artist1', name: 'Artiste 1', description: 'Chanteur populaire' },
      { id: 'artist2', name: 'Artiste 2', description: 'Rappeur talentueux' },
      { id: 'artist3', name: 'Artiste 3', description: 'Chanteuse internationale' },
    ],
  },
  {
    id: 'sports',
    name: 'Sportifs',
    icon: 'fitness',
    color: '#4ecdc4',
    candidates: [
      { id: 'sport1', name: 'Sportif 1', description: 'Champion de football' },
      { id: 'sport2', name: 'Sportif 2', description: 'Athlète olympique' },
      { id: 'sport3', name: 'Sportif 3', description: 'Joueur de tennis' },
    ],
  },
];

const DashboardScreen = ({ navigation }) => {
  const { user, logout } = useAuth();
  const [categories, setCategories] = useState([]);
  const [userVotes, setUserVotes] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Charger les catégories depuis l'API
      const formattedCategories = await dataService.getFormattedCategories();
      setCategories(formattedCategories);
      
      // Charger les votes de l'utilisateur depuis l'API
      if (user) {
        const votes = await dataService.getUserVotes(user.id);
        setUserVotes(votes);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
      Alert.alert('Erreur', 'Impossible de charger les données. Veuillez réessayer.');
      // Utiliser les données de fallback
      setCategories(FALLBACK_CATEGORIES);
    } finally {
      setLoading(false);
    }
  };

  const handleVoteForCategory = (category) => {
    const currentVote = dataService.getUserVoteForCategory(category.id);
    navigation.navigate('Voting', { 
      category,
      currentVote
    });
  };

  const handleSubmitAllVotes = async () => {
    const totalCategories = categories.length;
    const votedCategories = userVotes.length;

    if (votedCategories < totalCategories) {
      Alert.alert(
        'Votes incomplets',
        `Vous devez voter dans toutes les catégories (${votedCategories}/${totalCategories} complétées).`,
        [{ text: 'OK' }]
      );
      return;
    }

    Alert.alert(
      'Confirmer l\'envoi',
      'Êtes-vous sûr de vouloir envoyer tous vos votes ? Cette action est définitive.',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Confirmer',
          onPress: async () => {
            try {
              // Les votes sont déjà envoyés via l'API lors du vote individuel
              navigation.navigate('Confirmation');
            } catch (error) {
              Alert.alert('Erreur', 'Impossible d\'envoyer les votes. Veuillez réessayer.');
            }
          },
        },
      ]
    );
  };

  const handleLogout = async () => {
    Alert.alert(
      'Déconnexion',
      'Êtes-vous sûr de vouloir vous déconnecter ?',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Déconnecter',
          onPress: async () => {
            await logout();
            navigation.navigate('Home');
          },
        },
      ]
    );
  };

  const renderCategoryCard = ({ item: category }) => {
    const hasVoted = dataService.getUserVoteForCategory(category.id);
    const votedCandidate = hasVoted 
      ? category.candidates.find(c => c.id === hasVoted)
      : null;

    return (
      <TouchableOpacity
        style={[styles.categoryCard, hasVoted && styles.categoryCardVoted]}
        onPress={() => handleVoteForCategory(category)}
        activeOpacity={0.8}
      >
        <View style={styles.categoryHeader}>
          <View style={[styles.categoryIcon, { backgroundColor: category.color }]}>
            <Ionicons name={category.icon} size={24} color="#fff" />
          </View>
          <View style={styles.categoryInfo}>
            <Text style={styles.categoryName}>{category.name}</Text>
            <Text style={styles.categoryCount}>
              {category.candidates.length} candidats
            </Text>
          </View>
          <View style={styles.categoryStatus}>
            {hasVoted ? (
              <Ionicons name="checkmark-circle" size={24} color="#4CAF50" />
            ) : (
              <Ionicons name="ellipse-outline" size={24} color="#b0b0b0" />
            )}
          </View>
        </View>

        {hasVoted && votedCandidate && (
          <View style={styles.voteInfo}>
            <Text style={styles.voteLabel}>Votre vote :</Text>
            <Text style={styles.votedCandidate}>{votedCandidate.name}</Text>
          </View>
        )}

        <View style={styles.categoryFooter}>
          <Text style={styles.categoryAction}>
            {hasVoted ? 'Modifier le vote' : 'Voter maintenant'}
          </Text>
          <Ionicons name="chevron-forward" size={16} color="#b0b0b0" />
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Chargement...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const totalCategories = categories.length;
  const votedCategories = userVotes.length;
  const allVotesComplete = votedCategories === totalCategories;

  return (
    <GradientView gradient="premium" style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <ScrollView style={styles.scrollView}>
          {/* Header utilisateur */}
          <Card variant="glass" style={styles.userHeader}>
            <View style={styles.userInfo}>
              <Ionicons name="person-circle" size={50} color={theme.colors.gold} />
              <View style={styles.userDetails}>
                <Text style={styles.userName}>
                  {user ? `${user.firstName || ''} ${user.lastName || ''}` : 'Utilisateur'}
                </Text>
                <Text style={styles.userEmail}>
                  {user ? user.email : ''}
                </Text>
              </View>
            </View>
            <View style={styles.headerActions}>
              <Button
                variant="ghost"
                size="small"
                icon="time"
                onPress={() => navigation.navigate('History')}
                style={styles.historyButton}
              />
              <Button
                variant="ghost"
                size="small"
                icon="log-out"
                onPress={handleLogout}
                style={styles.logoutButton}
              />
            </View>
          </Card>

        {/* Progression */}
        <Card variant="elevated" style={styles.progressSection}>
          <Text style={styles.progressTitle}>Progression des votes</Text>
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progressFill, 
                { width: `${(votedCategories / totalCategories) * 100}%` }
              ]} 
            />
          </View>
          <Text style={styles.progressText}>
            {votedCategories}/{totalCategories} catégories complétées
          </Text>
        </Card>

        {/* Liste des catégories */}
        <Card variant="primary" style={styles.categoriesSection}>
          <Text style={styles.sectionTitle}>Catégories de vote</Text>
          <FlatList
            data={categories}
            renderItem={renderCategoryCard}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        </Card>

        {/* Bouton d'envoi final */}
        <Card variant="accent" style={styles.submitSection}>
          <Button
            title="Envoyer tous mes votes"
            onPress={handleSubmitAllVotes}
            variant="primary"
            size="large"
            icon="send"
            disabled={!allVotesComplete}
            style={styles.submitButton}
          />
          
          {!allVotesComplete && (
            <Text style={styles.submitHint}>
              Complétez tous les votes pour pouvoir les envoyer
            </Text>
          )}
        </Card>
      </ScrollView>
      </SafeAreaView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: theme.colors.textPrimary,
    fontSize: theme.typography.fontSize.lg,
  },
  scrollView: {
    flex: 1,
    padding: theme.spacing.md,
  },
  userHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  userDetails: {
    marginLeft: theme.spacing.md,
    flex: 1,
  },
  userName: {
    color: theme.colors.textPrimary,
    fontSize: theme.typography.fontSize.xl,
    fontWeight: theme.typography.fontWeight.bold,
  },
  userEmail: {
    color: theme.colors.textSecondary,
    fontSize: theme.typography.fontSize.sm,
    marginTop: theme.spacing.xs,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  historyButton: {
    marginRight: theme.spacing.sm,
  },
  logoutButton: {
    // Styles gérés par le composant Button
  },
  progressSection: {
    marginBottom: theme.spacing.lg,
  },
  progressTitle: {
    color: theme.colors.textPrimary,
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.bold,
    marginBottom: theme.spacing.md,
  },
  progressBar: {
    height: 8,
    backgroundColor: theme.colors.surfaceLight,
    borderRadius: theme.borderRadius.sm,
    marginBottom: theme.spacing.sm,
  },
  progressFill: {
    height: '100%',
    backgroundColor: theme.colors.gold,
    borderRadius: theme.borderRadius.sm,
  },
  progressText: {
    color: theme.colors.textSecondary,
    fontSize: theme.typography.fontSize.sm,
  },
  categoriesSection: {
    marginBottom: theme.spacing.lg,
  },
  sectionTitle: {
    color: theme.colors.textPrimary,
    fontSize: theme.typography.fontSize['2xl'],
    fontWeight: theme.typography.fontWeight.bold,
    marginBottom: theme.spacing.md,
  },
  categoryCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    borderWidth: 1,
    borderColor: theme.colors.surfaceLight,
  },
  categoryCardVoted: {
    borderColor: theme.colors.gold,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryInfo: {
    flex: 1,
    marginLeft: theme.spacing.sm,
  },
  categoryName: {
    color: theme.colors.textPrimary,
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.bold,
  },
  categoryCount: {
    color: theme.colors.textSecondary,
    fontSize: theme.typography.fontSize.sm,
    marginTop: theme.spacing.xs,
  },
  categoryStatus: {
    marginLeft: 10,
  },
  voteInfo: {
    backgroundColor: theme.colors.surfaceLight,
    padding: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
    marginBottom: theme.spacing.sm,
  },
  voteLabel: {
    color: theme.colors.textSecondary,
    fontSize: theme.typography.fontSize.sm,
  },
  votedCandidate: {
    color: theme.colors.gold,
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.bold,
    marginTop: theme.spacing.xs,
  },
  categoryFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryAction: {
    color: theme.colors.gold,
    fontSize: theme.typography.fontSize.base,
  },
  submitSection: {
    alignItems: 'center',
  },
  submitButton: {
    width: '100%',
  },
  submitHint: {
    color: theme.colors.textSecondary,
    fontSize: theme.typography.fontSize.sm,
    textAlign: 'center',
    marginTop: theme.spacing.md,
  },
});

export default DashboardScreen;
