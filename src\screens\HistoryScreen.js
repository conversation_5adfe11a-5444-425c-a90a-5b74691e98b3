import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  FlatList,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Données des catégories (même structure que DashboardScreen)
const CATEGORIES_DATA = [
  {
    id: 'artists',
    name: 'Artistes',
    icon: 'musical-notes',
    color: '#ff6b6b',
    candidates: [
      { id: 'artist1', name: 'Artiste 1', description: 'Chanteur populaire' },
      { id: 'artist2', name: 'Artiste 2', description: 'Rappeur talentueux' },
      { id: 'artist3', name: 'Artiste 3', description: 'Chanteuse internationale' },
    ],
  },
  {
    id: 'sports',
    name: 'Sportifs',
    icon: 'fitness',
    color: '#4ecdc4',
    candidates: [
      { id: 'sport1', name: 'Sportif 1', description: 'Champion de football' },
      { id: 'sport2', name: 'Sportif 2', description: 'Athlète olympique' },
      { id: 'sport3', name: 'Sportif 3', description: 'Joueur de tennis' },
    ],
  },
  {
    id: 'influencers',
    name: 'Influenceurs',
    icon: 'people',
    color: '#45b7d1',
    candidates: [
      { id: 'inf1', name: 'Influenceur 1', description: 'Créateur de contenu' },
      { id: 'inf2', name: 'Influenceur 2', description: 'YouTubeur populaire' },
      { id: 'inf3', name: 'Influenceur 3', description: 'Star des réseaux sociaux' },
    ],
  },
  {
    id: 'media',
    name: 'Médias',
    icon: 'tv',
    color: '#f9ca24',
    candidates: [
      { id: 'media1', name: 'Média 1', description: 'Chaîne TV populaire' },
      { id: 'media2', name: 'Média 2', description: 'Podcast influent' },
      { id: 'media3', name: 'Média 3', description: 'Magazine en ligne' },
    ],
  },
];

const HistoryScreen = ({ navigation }) => {
  const [userData, setUserData] = useState(null);
  const [votes, setVotes] = useState({});
  const [votesSubmitted, setVotesSubmitted] = useState(false);
  const [submissionDate, setSubmissionDate] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      // Charger les données utilisateur
      const userDataStr = await AsyncStorage.getItem('userData');
      if (userDataStr) {
        setUserData(JSON.parse(userDataStr));
      }

      // Charger les votes
      const votesStr = await AsyncStorage.getItem('userVotes');
      if (votesStr) {
        setVotes(JSON.parse(votesStr));
      }

      // Vérifier si les votes ont été soumis
      const submitted = await AsyncStorage.getItem('votesSubmitted');
      setVotesSubmitted(submitted === 'true');

      // Charger la date de soumission
      const submissionDateStr = await AsyncStorage.getItem('submissionDate');
      if (submissionDateStr) {
        setSubmissionDate(new Date(submissionDateStr));
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
    } finally {
      setLoading(false);
    }
  };

  const getVotedCandidate = (categoryId) => {
    const voteId = votes[categoryId];
    if (!voteId) return null;

    const category = CATEGORIES_DATA.find(cat => cat.id === categoryId);
    return category?.candidates.find(candidate => candidate.id === voteId);
  };

  const formatDate = (date) => {
    if (!date) return '';
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const renderVoteItem = ({ item: category }) => {
    const votedCandidate = getVotedCandidate(category.id);
    const hasVoted = !!votedCandidate;

    return (
      <View style={styles.voteCard}>
        <View style={styles.voteHeader}>
          <View style={[styles.categoryIcon, { backgroundColor: category.color }]}>
            <Ionicons name={category.icon} size={24} color="#fff" />
          </View>
          <View style={styles.categoryInfo}>
            <Text style={styles.categoryName}>{category.name}</Text>
            <Text style={styles.categoryStatus}>
              {hasVoted ? 'Vote enregistré' : 'Aucun vote'}
            </Text>
          </View>
          <View style={styles.voteStatus}>
            {hasVoted ? (
              <Ionicons name="checkmark-circle" size={24} color="#4CAF50" />
            ) : (
              <Ionicons name="close-circle" size={24} color="#f44336" />
            )}
          </View>
        </View>

        {hasVoted && (
          <View style={styles.voteDetails}>
            <Text style={styles.voteLabel}>Votre choix :</Text>
            <View style={styles.candidateInfo}>
              <View style={[styles.candidateAvatar, { backgroundColor: category.color }]}>
                <Text style={styles.candidateInitial}>
                  {votedCandidate.name.charAt(0).toUpperCase()}
                </Text>
              </View>
              <View style={styles.candidateDetails}>
                <Text style={styles.candidateName}>{votedCandidate.name}</Text>
                <Text style={styles.candidateDescription}>{votedCandidate.description}</Text>
              </View>
            </View>
          </View>
        )}
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Chargement...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const totalVotes = Object.keys(votes).length;
  const totalCategories = CATEGORIES_DATA.length;

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Header utilisateur */}
        <View style={styles.userHeader}>
          <View style={styles.userInfo}>
            <Ionicons name="person-circle" size={50} color="#ffd700" />
            <View style={styles.userDetails}>
              <Text style={styles.userName}>
                {userData ? `${userData.firstName} ${userData.lastName}` : 'Utilisateur'}
              </Text>
              <Text style={styles.userEmail}>
                {userData ? userData.email : ''}
              </Text>
            </View>
          </View>
        </View>

        {/* Statut des votes */}
        <View style={styles.statusSection}>
          <Text style={styles.sectionTitle}>Statut de vos votes</Text>
          
          <View style={styles.statusCard}>
            <View style={styles.statusRow}>
              <Text style={styles.statusLabel}>Votes complétés :</Text>
              <Text style={styles.statusValue}>{totalVotes}/{totalCategories}</Text>
            </View>
            
            <View style={styles.statusRow}>
              <Text style={styles.statusLabel}>Statut :</Text>
              <Text style={[
                styles.statusValue,
                votesSubmitted ? styles.statusSubmitted : styles.statusPending
              ]}>
                {votesSubmitted ? 'Envoyés' : 'En cours'}
              </Text>
            </View>
            
            {submissionDate && (
              <View style={styles.statusRow}>
                <Text style={styles.statusLabel}>Date d'envoi :</Text>
                <Text style={styles.statusValue}>{formatDate(submissionDate)}</Text>
              </View>
            )}
          </View>

          {!votesSubmitted && totalVotes < totalCategories && (
            <TouchableOpacity
              style={styles.continueButton}
              onPress={() => navigation.navigate('Dashboard')}
              activeOpacity={0.8}
            >
              <Ionicons name="arrow-forward" size={20} color="#fff" />
              <Text style={styles.continueButtonText}>
                Continuer mes votes
              </Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Liste des votes */}
        <View style={styles.votesSection}>
          <Text style={styles.sectionTitle}>Détail de vos votes</Text>
          <FlatList
            data={CATEGORIES_DATA}
            renderItem={renderVoteItem}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        </View>

        {/* Message informatif */}
        <View style={styles.infoSection}>
          <View style={styles.infoCard}>
            <Ionicons name="information-circle" size={24} color="#45b7d1" />
            <Text style={styles.infoText}>
              {votesSubmitted
                ? 'Vos votes ont été enregistrés définitivement. Vous recevrez une notification avec les résultats du tirage au sort.'
                : 'Vous pouvez encore modifier vos votes jusqu\'à leur envoi final depuis le tableau de bord.'
              }
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0f0f23',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#fff',
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
    paddingBottom: 100, // Extra padding for bottom navigation
  },
  userHeader: {
    padding: 20,
    backgroundColor: '#1a1a2e',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  userDetails: {
    marginLeft: 15,
    flex: 1,
  },
  userName: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  userEmail: {
    color: '#b0b0b0',
    fontSize: 14,
    marginTop: 2,
  },
  statusSection: {
    padding: 20,
  },
  sectionTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  statusCard: {
    backgroundColor: '#1a1a2e',
    borderRadius: 12,
    padding: 15,
    marginBottom: 15,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusLabel: {
    color: '#b0b0b0',
    fontSize: 14,
  },
  statusValue: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  statusSubmitted: {
    color: '#4CAF50',
  },
  statusPending: {
    color: '#ff9800',
  },
  continueButton: {
    backgroundColor: '#45b7d1',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
  },
  continueButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  votesSection: {
    padding: 20,
  },
  voteCard: {
    backgroundColor: '#1a1a2e',
    borderRadius: 12,
    padding: 15,
    marginBottom: 12,
  },
  voteHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryInfo: {
    flex: 1,
    marginLeft: 12,
  },
  categoryName: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  categoryStatus: {
    color: '#b0b0b0',
    fontSize: 12,
    marginTop: 2,
  },
  voteStatus: {
    marginLeft: 10,
  },
  voteDetails: {
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: '#2a2a3e',
  },
  voteLabel: {
    color: '#b0b0b0',
    fontSize: 12,
    marginBottom: 8,
  },
  candidateInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  candidateAvatar: {
    width: 35,
    height: 35,
    borderRadius: 17.5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  candidateInitial: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  candidateDetails: {
    marginLeft: 12,
    flex: 1,
  },
  candidateName: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  candidateDescription: {
    color: '#b0b0b0',
    fontSize: 12,
    marginTop: 2,
  },
  infoSection: {
    padding: 20,
  },
  infoCard: {
    backgroundColor: '#16213e',
    borderRadius: 12,
    padding: 15,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  infoText: {
    color: '#b0b0b0',
    fontSize: 14,
    lineHeight: 20,
    marginLeft: 12,
    flex: 1,
  },
});

export default HistoryScreen;
