import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import theme from '../../theme/theme';

const CategoryIcon = ({ 
  icon, 
  label, 
  onPress, 
  style,
  size = 'medium' 
}) => {
  const getIconSize = () => {
    switch (size) {
      case 'small': return 20;
      case 'large': return 32;
      default: return 24;
    }
  };

  const getContainerSize = () => {
    switch (size) {
      case 'small': return 50;
      case 'large': return 70;
      default: return 60;
    }
  };

  return (
    <TouchableOpacity 
      style={[styles.container, style]} 
      onPress={onPress}
      activeOpacity={0.8}
    >
      <LinearGradient
        colors={theme.colors.gradients.cta}
        style={[
          styles.iconContainer,
          {
            width: getContainerSize(),
            height: getContainerSize(),
            borderRadius: getContainerSize() / 2
          }
        ]}
      >
        <Ionicons
          name={icon}
          size={getIconSize()}
          color={theme.colors.black}
        />
      </LinearGradient>
      
      {label && (
        <Text style={styles.label} numberOfLines={1}>
          {label}
        </Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 8,
    marginVertical: 8,
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: theme.colors.primary,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  label: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginTop: 8,
    fontWeight: '500',
    maxWidth: 80,
  },
});

export default CategoryIcon;
