import React from 'react';
import { View, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import theme from '../../theme/theme';

const GradientView = ({
  children,
  gradient = 'primary',
  style,
  start = { x: 0, y: 0 },
  end = { x: 1, y: 1 },
  ...props
}) => {
  const getGradientColors = () => {
    switch (gradient) {
      case 'primary':
        return theme.colors.gradients.primary;
      case 'accent':
        return theme.colors.gradients.accent;
      case 'dark':
        return theme.colors.gradients.dark;
      case 'gold':
        return theme.colors.gradients.gold;
      case 'blackToGold':
        return ['#000000', '#1a1a1a', '#ffd700'];
      case 'goldToBlack':
        return ['#ffd700', '#b8860b', '#000000'];
      case 'glass':
        return ['rgba(0, 0, 0, 0.8)', 'rgba(0, 0, 0, 0.6)', 'rgba(255, 215, 0, 0.1)'];
      default:
        return theme.colors.gradients.primary;
    }
  };

  return (
    <LinearGradient
      colors={getGradientColors()}
      start={start}
      end={end}
      style={[styles.gradient, style]}
      {...props}
    >
      {children}
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  gradient: {
    flex: 1,
  },
});

export default GradientView;
