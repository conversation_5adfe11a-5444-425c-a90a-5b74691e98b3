# ninja log v5
3	108	0	C:/Users/<USER>/Desktop/awards/awardsApp/android/app/.cxx/Debug/6a5k4y29/x86_64/CMakeFiles/cmake.verify_globs	ec50275c6551199b
26	4109	7759973394480986	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	1924fcd54cdb6040
60	4551	7759973399205202	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o	3dafeb28a2161dd9
21	4739	7759973400707080	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	9d7f759a8125947
43	5383	7759973407472252	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	489abaeae1789192
37	5441	7759973407959492	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	cfbddfa3ca807a5f
31	5963	7759973412916216	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	b55e286ce00b80aa
14	6017	7759973413775919	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	ceeedceaecea5c58
82	6097	7759973413841072	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o	eec16da1efbc176a
9	6272	7759973416385140	CMakeFiles/appmodules.dir/OnLoad.cpp.o	1be7e2247029f568
48	6407	7759973417612837	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	2b64b9816a23bb49
54	7421	7759973427289776	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o	8ad2d9fa9dc28e3
71	7434	7759973427841992	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/96f9eeb2633a33651c400c9ced96984f/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o	4e46a635bfc9933a
65	8001	7759973432631003	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o	e054799609549866
4118	8685	7759973440580649	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o	8b6784b8207c59a8
4552	9477	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	a9c260b9896d4174
4740	9650	7759973449733245	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	34ab64443106163a
6272	10098	7759973454576311	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7fef278d7e13f2f72769aedb28204396/react/renderer/components/safeareacontext/States.cpp.o	e825f40856829862
5963	10534	7759973458785244	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/0bbed6a10d4a6b1f8c8aab2761e1fbb3/renderer/components/safeareacontext/EventEmitters.cpp.o	76ead794ec160d53
6018	10915	7759973462400396	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7cd4a2f4725709ed36b165294390a0b8/components/safeareacontext/RNCSafeAreaViewState.cpp.o	7a4a7c28a6b2b056
5442	10924	7759973462896020	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/0bbed6a10d4a6b1f8c8aab2761e1fbb3/renderer/components/safeareacontext/ShadowNodes.cpp.o	a0c39ff66a682544
6097	11934	7759973473006629	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7fef278d7e13f2f72769aedb28204396/react/renderer/components/safeareacontext/Props.cpp.o	111bc94b0301e9ac
5383	12209	7759973475539903	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/e18682d777342c655410643a6594f117/components/safeareacontext/ComponentDescriptors.cpp.o	3b079c2f92bf5c28
7422	12356	7759973477187417	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c64baa380ede5b93ef2bde6d021070a7/safeareacontext/safeareacontextJSI-generated.cpp.o	8d204dfea65b9fde
7435	12605	7759973479563092	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/e9eda455ea03ba9e7d6d618514f99095/source/codegen/jni/safeareacontext-generated.cpp.o	7a78086a0af9eaad
6407	12666	7759973480233849	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e02703ba2d835cf3f13d1ac0dfaa68a7/react/renderer/components/rnscreens/RNSBottomTabsShadowNode.cpp.o	b2e45eb4130041c8
12606	13246	7759973485496201	C:/Users/<USER>/Desktop/awards/awardsApp/android/app/build/intermediates/cxx/Debug/6a5k4y29/obj/x86_64/libreact_codegen_safeareacontext.so	d7b9d68a64fe3461
8001	13919	7759973492587596	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e02703ba2d835cf3f13d1ac0dfaa68a7/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o	8cc4d7c1cb0ec5e4
9477	14415	7759973497740641	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/4ebd47a9cc6e91a3a47533312a70f82d/cpp/react/renderer/components/rnscreens/RNSBottomTabsState.cpp.o	acd0f9a427eafe54
8685	14941	7759973503123160	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/03c8a8f2e930c932133adb51cf8c5ba3/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	5ad091d49363c909
10535	15413	7759973507767397	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/03c8a8f2e930c932133adb51cf8c5ba3/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	9759dcb1aa9ebb62
3	15562	7759973508222624	CMakeFiles/appmodules.dir/C_/Users/<USER>/Desktop/awards/awardsApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	cf29f6676fc18883
9650	15658	7759973510197152	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/4ebd47a9cc6e91a3a47533312a70f82d/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	2428486a7f66d1b8
10925	15907	7759973512820695	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d61ef2c0dc15ae9df314df0fe33789b5/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o	535fac376e660637
10099	16143	7759973514911824	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a3ab5e2e9ddf6e46b60687ddb096364c/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	bb78adc31e8342db
12210	17063	7759973524090640	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/03c8a8f2e930c932133adb51cf8c5ba3/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	2c4777f63a277cb4
10915	17482	7759973528389530	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	3cdb47a624bb9c88
12356	17666	7759973530382376	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a3ab5e2e9ddf6e46b60687ddb096364c/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	95c6c136d9c9ae28
11934	17909	7759973532799009	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/03c8a8f2e930c932133adb51cf8c5ba3/renderer/components/rnscreens/RNSSplitViewScreenShadowNode.cpp.o	7e8fcbde5266f16c
14415	18430	7759973537873574	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o	9b276734574a3d0
13247	18439	7759973537903581	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o	440966fc439e5cc6
13919	18841	7759973542087964	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o	7b9b53b370cd38e9
14941	19149	7759973545255300	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o	c32a78b205fe719e
16144	19446	7759973547858025	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1bf8b1edfd639b6fca47603b37e3d048/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o	76bec5e5ddbca8f7
15907	20328	7759973556849969	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/332f4af2aa21c9c433cc741f53c02c65/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	7c02a6eb9db71659
15658	21027	7759973563914218	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/cdb7ebe261e8d7398af89ad59952c322/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	e0ee520cc566ee85
17667	21151	7759973565167323	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o	d45998023a8d9508
17064	21782	7759973571430745	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o	3ed7f2bf3fb76564
17482	22076	7759973574477538	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o	bcec2ccc239bc989
18440	22299	7759973576710891	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o	23dee30eb63d2dee
15413	22356	7759973577091285	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1bf8b1edfd639b6fca47603b37e3d048/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o	a61ab39b18fd5c37
17910	22513	7759973578873339	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o	b4db305a34a87fd2
19149	22559	7759973579394165	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o	3bc5a6c0b62ec185
18841	23047	7759973584248967	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o	3819f1eed412ee90
18431	23072	7759973584464139	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o	7746b29ed1d81384
12667	23184	7759973585164235	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/332f4af2aa21c9c433cc741f53c02c65/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o	2b2161acd83bd45b
15562	23311	7759973586683045	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/cdb7ebe261e8d7398af89ad59952c322/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o	44e01fabf3726831
23312	23459	7759973588150817	C:/Users/<USER>/Desktop/awards/awardsApp/android/app/build/intermediates/cxx/Debug/6a5k4y29/obj/x86_64/libreact_codegen_rnscreens.so	9360b0d89d1b9195
19446	23479	7759973588600941	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o	f49cf11f3e2a2a7d
20329	23723	7759973591012731	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o	497bbdd3b6b06bb
23724	23881	7759973592403276	C:/Users/<USER>/Desktop/awards/awardsApp/android/app/build/intermediates/cxx/Debug/6a5k4y29/obj/x86_64/libappmodules.so	cfb9a386b433c92e
