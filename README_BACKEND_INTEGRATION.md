# Intégration Frontend-Backend - Awards App

## Vue d'ensemble

Cette application React Native est maintenant connectée à un backend Node.js/Express avec MongoDB. L'intégration permet une authentification sécurisée, la gestion des catégories et des stars, ainsi qu'un système de vote en temps réel.

## Architecture

### Services créés

1. **`src/services/api.js`** - Service principal pour les appels API
2. **`src/services/dataService.js`** - Service pour la gestion des données (catégories, stars, votes)
3. **`src/context/AuthContext.js`** - Contexte React pour la gestion de l'authentification
4. **`src/config/config.js`** - Configuration de l'environnement

### Écrans modifiés

- **`AuthScreen.js`** - Authentification avec validation et appel API
- **`HomeScreen.js`** - Chargement des catégories depuis l'API
- **`DashboardScreen.js`** - Affichage des catégories et votes depuis l'API
- **`VotingScreen.js`** - Soumission des votes via l'API
- **`App.js`** - Intégration du contexte d'authentification

## Configuration

### 1. URL de l'API

Modifiez l'URL de l'API dans `src/config/config.js` :

```javascript
development: {
  API_BASE_URL: 'http://localhost:5000/api', // Pour le développement local
  DEBUG: true,
},
production: {
  API_BASE_URL: 'https://your-production-api.com/api', // Pour la production
  DEBUG: false,
},
```

### 2. Pour Android

Si vous testez sur un émulateur Android, utilisez :
```javascript
API_BASE_URL: 'http://********:5000/api'
```

### 3. Pour iOS

Si vous testez sur un simulateur iOS, utilisez :
```javascript
API_BASE_URL: 'http://localhost:5000/api'
```

## Fonctionnalités implémentées

### Authentification

- **Inscription** : Validation des champs, création de compte via API
- **Connexion** : Authentification avec email/mot de passe
- **Déconnexion** : Suppression du token et nettoyage des données
- **Persistance** : Maintien de la session via AsyncStorage

### Gestion des données

- **Catégories** : Chargement depuis l'API avec formatage automatique
- **Stars** : Récupération des candidats par catégorie
- **Votes** : Soumission et récupération des votes utilisateur

### Sécurité

- **Cookies HTTP-only** : Authentification sécurisée
- **Validation** : Vérification des données côté client et serveur
- **Gestion d'erreurs** : Messages d'erreur appropriés

## Utilisation

### 1. Démarrer le backend

Assurez-vous que votre serveur backend est en cours d'exécution sur le port 5000.

### 2. Démarrer l'application

```bash
npm start
# ou
expo start
```

### 3. Tester l'authentification

1. Créez un compte avec un mot de passe valide (8+ caractères, majuscule, chiffre, caractère spécial)
2. Connectez-vous avec vos identifiants
3. Vérifiez que vous êtes redirigé vers le dashboard

### 4. Tester le vote

1. Sélectionnez une catégorie
2. Choisissez un candidat
3. Confirmez votre vote
4. Vérifiez que le vote est enregistré

## Gestion des erreurs

### Erreurs courantes

1. **Erreur de connexion** : Vérifiez que le backend est démarré
2. **Erreur CORS** : Assurez-vous que le backend accepte les requêtes cross-origin
3. **Erreur d'authentification** : Vérifiez les identifiants et le format du mot de passe

### Debug

Activez le mode debug dans `config.js` pour voir les logs détaillés :

```javascript
DEBUG: true
```

## Structure des données

### Format des catégories

```javascript
{
  id: "string",
  name: "string",
  description: "string",
  candidates: [
    {
      id: "string",
      name: "string",
      description: "string",
      photoUrl: "string",
      voteCount: number
    }
  ],
  color: "string",
  icon: "string"
}
```

### Format des votes

```javascript
{
  userId: "string",
  votes: [
    {
      category: "string",
      star: "string"
    }
  ]
}
```

## Développement futur

### Fonctionnalités à ajouter

1. **Gestion des images** : Upload et affichage des photos des stars
2. **Notifications** : Alertes pour les nouveaux votes
3. **Statistiques** : Affichage des résultats en temps réel
4. **Mode hors ligne** : Cache des données pour utilisation sans connexion

### Optimisations

1. **Cache** : Mise en cache des données fréquemment utilisées
2. **Pagination** : Chargement progressif des données
3. **Compression** : Optimisation des requêtes API

## Support

Pour toute question ou problème, consultez :
- La documentation de l'API dans `API_DOCUMENTATION.md`
- Les logs de l'application
- La documentation React Native et Expo 