import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import apiService from '../services/api';

const TokenDebug = () => {
  const [tokenInfo, setTokenInfo] = useState(null);
  const [testResults, setTestResults] = useState([]);

  useEffect(() => {
    checkTokenStatus();
  }, []);

  const checkTokenStatus = async () => {
    try {
      const userData = await AsyncStorage.getItem('userData');
      if (userData) {
        const parsed = JSON.parse(userData);
        setTokenInfo({
          hasToken: !!parsed.token,
          tokenLength: parsed.token ? parsed.token.length : 0,
          tokenPreview: parsed.token ? parsed.token.substring(0, 20) + '...' : 'None',
          userData: parsed,
        });
      } else {
        setTokenInfo({
          hasToken: false,
          tokenLength: 0,
          tokenPreview: 'None',
          userData: null,
        });
      }
    } catch (error) {
      console.error('Error checking token status:', error);
      setTokenInfo({
        hasToken: false,
        tokenLength: 0,
        tokenPreview: 'Error',
        userData: null,
      });
    }
  };

  const runTokenTests = async () => {
    setTestResults([]);
    const results = [];

    // Test 1: Vérifier le token stocké
    try {
      const userData = await AsyncStorage.getItem('userData');
      if (userData) {
        const parsed = JSON.parse(userData);
        results.push({
          test: 'Token Storage',
          success: 'success',
          message: `Token found: ${parsed.token ? 'Yes' : 'No'} (${parsed.token?.length || 0} chars)`
        });
      } else {
        results.push({
          test: 'Token Storage',
          success: 'error',
          message: 'No user data found in storage'
        });
      }
    } catch (error) {
      results.push({
        test: 'Token Storage',
        success: 'error',
        message: `Error: ${error.message}`
      });
    }

    // Test 2: Vérifier le token avec l'API
    try {
      const verifyResponse = await apiService.verifyToken();
      results.push({
        test: 'Token Verification',
        success: 'success',
        message: `Valid: ${verifyResponse.valid}, User: ${verifyResponse.user?.email || 'None'}`
      });
    } catch (error) {
      results.push({
        test: 'Token Verification',
        success: 'error',
        message: `Error: ${error.message}`
      });
    }

    // Test 3: Tester le profil utilisateur
    try {
      const profileResponse = await apiService.getUserProfile();
      results.push({
        test: 'User Profile',
        success: 'success',
        message: `Profile: ${profileResponse.user?.email || 'None'}`
      });
    } catch (error) {
      results.push({
        test: 'User Profile',
        success: 'error',
        message: `Error: ${error.message}`
      });
    }

    setTestResults(results);
  };

  const clearToken = async () => {
    try {
      await AsyncStorage.removeItem('userData');
      Alert.alert('Token cleared', 'User data has been removed from storage');
      checkTokenStatus();
    } catch (error) {
      Alert.alert('Error', 'Failed to clear token');
    }
  };

  const getResultIcon = (success) => {
    switch (success) {
      case 'success':
        return <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />;
      case 'error':
        return <Ionicons name="close-circle" size={16} color="#f44336" />;
      default:
        return <Ionicons name="help-circle" size={16} color="#FF9800" />;
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Debug des Tokens</Text>
      
      {/* Informations sur le token */}
      {tokenInfo && (
        <View style={styles.tokenInfo}>
          <Text style={styles.tokenTitle}>État du token :</Text>
          <Text style={styles.tokenText}>
            Présent : {tokenInfo.hasToken ? '✅ Oui' : '❌ Non'}
          </Text>
          <Text style={styles.tokenText}>
            Longueur : {tokenInfo.tokenLength} caractères
          </Text>
          <Text style={styles.tokenText}>
            Aperçu : {tokenInfo.tokenPreview}
          </Text>
        </View>
      )}

      {/* Boutons d'action */}
      <View style={styles.actions}>
        <TouchableOpacity
          style={styles.testButton}
          onPress={runTokenTests}
        >
          <Ionicons name="bug" size={16} color="#fff" />
          <Text style={styles.testButtonText}>Tester les tokens</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.clearButton}
          onPress={clearToken}
        >
          <Ionicons name="trash" size={16} color="#fff" />
          <Text style={styles.clearButtonText}>Effacer le token</Text>
        </TouchableOpacity>
      </View>

      {/* Résultats des tests */}
      {testResults.length > 0 && (
        <View style={styles.results}>
          <Text style={styles.resultsTitle}>Résultats des tests :</Text>
          <ScrollView style={styles.resultsList}>
            {testResults.map((result, index) => (
              <View key={index} style={styles.resultItem}>
                <View style={styles.resultHeader}>
                  {getResultIcon(result.success)}
                  <Text style={styles.resultTest}>{result.test}</Text>
                </View>
                <Text style={styles.resultMessage}>{result.message}</Text>
              </View>
            ))}
          </ScrollView>
        </View>
      )}

      {/* Instructions */}
      <View style={styles.instructions}>
        <Text style={styles.instructionsTitle}>Instructions :</Text>
        <Text style={styles.instructionsText}>
          1. Vérifiez que le token est stocké après la connexion{'\n'}
          2. Testez avec l'endpoint de vérification{'\n'}
          3. Vérifiez que le token est envoyé dans les headers{'\n'}
          4. Consultez les logs pour plus de détails
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#1a1a2e',
    padding: 20,
    borderRadius: 12,
    margin: 20,
  },
  title: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  tokenInfo: {
    backgroundColor: '#16213e',
    padding: 15,
    borderRadius: 8,
    marginBottom: 15,
  },
  tokenTitle: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  tokenText: {
    color: '#b0b0b0',
    fontSize: 12,
    marginBottom: 4,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  testButton: {
    backgroundColor: '#2196F3',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    flex: 0.48,
  },
  testButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  clearButton: {
    backgroundColor: '#f44336',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    flex: 0.48,
  },
  clearButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  results: {
    marginBottom: 15,
  },
  resultsTitle: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  resultsList: {
    maxHeight: 150,
  },
  resultItem: {
    backgroundColor: '#16213e',
    padding: 8,
    borderRadius: 6,
    marginBottom: 6,
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  resultTest: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 6,
  },
  resultMessage: {
    color: '#b0b0b0',
    fontSize: 10,
    marginLeft: 22,
  },
  instructions: {
    backgroundColor: '#16213e',
    padding: 12,
    borderRadius: 8,
  },
  instructionsTitle: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 6,
  },
  instructionsText: {
    color: '#b0b0b0',
    fontSize: 10,
    lineHeight: 14,
  },
});

export default TokenDebug; 