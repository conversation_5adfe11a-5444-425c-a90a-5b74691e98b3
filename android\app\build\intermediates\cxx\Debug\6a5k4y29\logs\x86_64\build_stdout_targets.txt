ninja: Entering directory `C:\Users\<USER>\Desktop\awards\awardsApp\android\app\.cxx\Debug\6a5k4y29\x86_64'
[0/2] Re-checking globbed directories...
[1/60] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o
[2/60] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o
[3/60] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o
[4/60] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o
[5/60] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o
[6/60] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o
[7/60] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o
[8/60] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o
[9/60] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[10/60] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o
[11/60] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o
[12/60] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/96f9eeb2633a33651c400c9ced96984f/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o
[13/60] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o
[14/60] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o
[15/60] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o
[16/60] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o
[17/60] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7fef278d7e13f2f72769aedb28204396/react/renderer/components/safeareacontext/States.cpp.o
[18/60] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/0bbed6a10d4a6b1f8c8aab2761e1fbb3/renderer/components/safeareacontext/EventEmitters.cpp.o
[19/60] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7cd4a2f4725709ed36b165294390a0b8/components/safeareacontext/RNCSafeAreaViewState.cpp.o
[20/60] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/0bbed6a10d4a6b1f8c8aab2761e1fbb3/renderer/components/safeareacontext/ShadowNodes.cpp.o
[21/60] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7fef278d7e13f2f72769aedb28204396/react/renderer/components/safeareacontext/Props.cpp.o
[22/60] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/e18682d777342c655410643a6594f117/components/safeareacontext/ComponentDescriptors.cpp.o
[23/60] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c64baa380ede5b93ef2bde6d021070a7/safeareacontext/safeareacontextJSI-generated.cpp.o
[24/60] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/e9eda455ea03ba9e7d6d618514f99095/source/codegen/jni/safeareacontext-generated.cpp.o
[25/60] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e02703ba2d835cf3f13d1ac0dfaa68a7/react/renderer/components/rnscreens/RNSBottomTabsShadowNode.cpp.o
[26/60] Linking CXX shared library C:\Users\<USER>\Desktop\awards\awardsApp\android\app\build\intermediates\cxx\Debug\6a5k4y29\obj\x86_64\libreact_codegen_safeareacontext.so
[27/60] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e02703ba2d835cf3f13d1ac0dfaa68a7/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o
[28/60] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/4ebd47a9cc6e91a3a47533312a70f82d/cpp/react/renderer/components/rnscreens/RNSBottomTabsState.cpp.o
[29/60] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/03c8a8f2e930c932133adb51cf8c5ba3/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o
[30/60] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/03c8a8f2e930c932133adb51cf8c5ba3/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o
[31/60] Building CXX object CMakeFiles/appmodules.dir/C_/Users/<USER>/Desktop/awards/awardsApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[32/60] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/4ebd47a9cc6e91a3a47533312a70f82d/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o
[33/60] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d61ef2c0dc15ae9df314df0fe33789b5/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o
[34/60] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a3ab5e2e9ddf6e46b60687ddb096364c/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o
[35/60] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/03c8a8f2e930c932133adb51cf8c5ba3/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o
[36/60] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o
[37/60] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a3ab5e2e9ddf6e46b60687ddb096364c/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o
[38/60] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/03c8a8f2e930c932133adb51cf8c5ba3/renderer/components/rnscreens/RNSSplitViewScreenShadowNode.cpp.o
[39/60] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o
[40/60] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o
[41/60] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o
[42/60] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o
[43/60] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1bf8b1edfd639b6fca47603b37e3d048/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o
[44/60] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/332f4af2aa21c9c433cc741f53c02c65/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o
[45/60] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/cdb7ebe261e8d7398af89ad59952c322/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o
[46/60] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o
[47/60] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o
[48/60] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o
[49/60] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o
[50/60] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1bf8b1edfd639b6fca47603b37e3d048/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o
[51/60] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o
[52/60] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o
[53/60] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o
[54/60] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o
[55/60] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/332f4af2aa21c9c433cc741f53c02c65/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o
[56/60] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/cdb7ebe261e8d7398af89ad59952c322/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o
[57/60] Linking CXX shared library C:\Users\<USER>\Desktop\awards\awardsApp\android\app\build\intermediates\cxx\Debug\6a5k4y29\obj\x86_64\libreact_codegen_rnscreens.so
[58/60] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o
[59/60] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o
[60/60] Linking CXX shared library C:\Users\<USER>\Desktop\awards\awardsApp\android\app\build\intermediates\cxx\Debug\6a5k4y29\obj\x86_64\libappmodules.so
