import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Text, TextInput } from 'react-native';
import { useFonts, Poppins_400Regular, Poppins_500Medium, Poppins_600SemiBold, Poppins_700Bold } from '@expo-google-fonts/poppins';

// Import context
import { AuthProvider } from './src/context/AuthContext';

// Import screens
import HomeScreen from './src/screens/HomeScreen';
import AuthScreen from './src/screens/AuthScreen';
import DashboardScreen from './src/screens/DashboardScreen';
import VotingScreen from './src/screens/VotingScreen';
import ConfirmationScreen from './src/screens/ConfirmationScreen';
import HistoryScreen from './src/screens/HistoryScreen';

const Stack = createStackNavigator();

export default function App() {
	const [fontsLoaded] = useFonts({
		Poppins_400Regular,
		Poppins_500Medium,
		Poppins_600SemiBold,
		Poppins_700Bold,
	});

	if (!fontsLoaded) {
		return null;
	}

	// Appliquer la police par défaut globalement
	if (Text.defaultProps == null) Text.defaultProps = {};
	if (TextInput.defaultProps == null) TextInput.defaultProps = {};
	Text.defaultProps.style = [{ fontFamily: 'Poppins_400Regular' }, Text.defaultProps.style];
	TextInput.defaultProps.style = [{ fontFamily: 'Poppins_400Regular' }, TextInput.defaultProps.style];

	return (
		<AuthProvider>
			<SafeAreaProvider>
					<NavigationContainer>
						<Stack.Navigator initialRouteName="Home" screenOptions={{ headerShown: false }}>
							<Stack.Screen name="Home" component={HomeScreen} />
							<Stack.Screen name="Auth" component={AuthScreen} />
							<Stack.Screen name="Dashboard" component={DashboardScreen} />
							<Stack.Screen name="Voting" component={VotingScreen} />
							<Stack.Screen name="Confirmation" component={ConfirmationScreen} />
							<Stack.Screen name="History" component={HistoryScreen} />
						</Stack.Navigator>
						<StatusBar style="light" />
					</NavigationContainer>
			</SafeAreaProvider>
		</AuthProvider>
	);
}
