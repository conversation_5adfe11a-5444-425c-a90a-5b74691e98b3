import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Text, TextInput, View } from 'react-native';
import { useFonts, Poppins_400Regular, Poppins_500Medium, Poppins_600SemiBold, Poppins_700Bold } from '@expo-google-fonts/poppins';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';

// Import context
import { AuthProvider } from './src/context/AuthContext';

// Import screens
import HomeScreen from './src/screens/HomeScreen';
import AuthScreen from './src/screens/AuthScreen';
import DashboardScreen from './src/screens/DashboardScreen';
import VotingScreen from './src/screens/VotingScreen';
import ConfirmationScreen from './src/screens/ConfirmationScreen';
import HistoryScreen from './src/screens/HistoryScreen';

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

// Bottom Tab Navigator Component
function MainTabNavigator() {
	return (
		<Tab.Navigator
  screenOptions={({ route }) => ({
    headerShown: false,
    tabBarIcon: ({ focused, color, size }) => {
      let iconName;

      if (route.name === "Home") {
        iconName = focused ? "home" : "home-outline";
      } else if (route.name === "Dashboard") {
        iconName = focused ? "heart" : "heart-outline";
      } else if (route.name === "Profile") {
        iconName = focused ? "person" : "person-outline";
      }

      return <Ionicons name={iconName} size={26} color={color} />;
    },
    tabBarActiveTintColor: "#fff",
    tabBarInactiveTintColor: "#aaa",
    tabBarShowLabel: false,
    tabBarStyle: {
      position: "absolute",
      height: 80,
	  borderRadius: 25,
	  marginHorizontal: 50,
	  marginBottom: 34,
      overflow: "hidden", // important to clip the blur
      borderTopWidth: 0,
	  paddingTop: 20,
    },
    tabBarBackground: () => (
      <BlurView
        tint="dark" // gives the glass effect
        intensity={60} // 0 = transparent, 100 = strong blur
        style={{ flex: 1, backgroundColor: "rgba(28, 28, 28, 0.65)" }} 
        // ^ overlay a brownish transparent background
      />
    ),
  })}
>
  <Tab.Screen name="Home" component={HomeScreen} />
  <Tab.Screen name="Dashboard" component={DashboardScreen} />
  <Tab.Screen name="Profile" component={HistoryScreen} />
</Tab.Navigator>
	);
}

export default function App() {
	const [fontsLoaded] = useFonts({
		Poppins_400Regular,
		Poppins_500Medium,
		Poppins_600SemiBold,
		Poppins_700Bold,
	});

	if (!fontsLoaded) {
		return null;
	}

	// Appliquer la police par défaut globalement
	if (Text.defaultProps == null) Text.defaultProps = {};
	if (TextInput.defaultProps == null) TextInput.defaultProps = {};
	Text.defaultProps.style = [{ fontFamily: 'Poppins_400Regular' }, Text.defaultProps.style];
	TextInput.defaultProps.style = [{ fontFamily: 'Poppins_400Regular' }, TextInput.defaultProps.style];

	return (
		<AuthProvider>
			<SafeAreaProvider>
					<NavigationContainer>
						<Stack.Navigator initialRouteName="MainTabs" screenOptions={{ headerShown: false }}>
							<Stack.Screen name="MainTabs" component={MainTabNavigator} />
							<Stack.Screen name="Auth" component={AuthScreen} />
							<Stack.Screen name="Voting" component={VotingScreen} />
							<Stack.Screen name="Confirmation" component={ConfirmationScreen} />
						</Stack.Navigator>
						<StatusBar style="light" />
					</NavigationContainer>
			</SafeAreaProvider>
		</AuthProvider>
	);
}
