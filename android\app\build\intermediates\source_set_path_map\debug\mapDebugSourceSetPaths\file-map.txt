com.sloumache.awardsApp.app-tracing-ktx-1.2.0-0 C:\Users\<USER>\.gradle\caches\8.13\transforms\05c532ea6eeed5ba96fff64378302997\transformed\tracing-ktx-1.2.0\res
com.sloumache.awardsApp.app-appcompat-resources-1.7.0-1 C:\Users\<USER>\.gradle\caches\8.13\transforms\067eb4768d0977cc253e099ab589c211\transformed\appcompat-resources-1.7.0\res
com.sloumache.awardsApp.app-activity-ktx-1.8.0-2 C:\Users\<USER>\.gradle\caches\8.13\transforms\0b3cd90608d731515ee5b0c8986ce7d6\transformed\activity-ktx-1.8.0\res
com.sloumache.awardsApp.app-lifecycle-livedata-core-ktx-2.6.2-3 C:\Users\<USER>\.gradle\caches\8.13\transforms\0e45932910c15ea66169daadac963681\transformed\lifecycle-livedata-core-ktx-2.6.2\res
com.sloumache.awardsApp.app-startup-runtime-1.1.1-4 C:\Users\<USER>\.gradle\caches\8.13\transforms\12f7ec3034c44ad7915028fd2e88ba60\transformed\startup-runtime-1.1.1\res
com.sloumache.awardsApp.app-constraintlayout-2.0.1-5 C:\Users\<USER>\.gradle\caches\8.13\transforms\162c641a6ded6de9dcbbe71bb887fa9c\transformed\constraintlayout-2.0.1\res
com.sloumache.awardsApp.app-savedstate-1.2.1-6 C:\Users\<USER>\.gradle\caches\8.13\transforms\19f8795450958262f34e41d8151462d1\transformed\savedstate-1.2.1\res
com.sloumache.awardsApp.app-fragment-1.6.1-7 C:\Users\<USER>\.gradle\caches\8.13\transforms\22d97c11c7e69f31bac4e14a1df1d05a\transformed\fragment-1.6.1\res
com.sloumache.awardsApp.app-lifecycle-runtime-ktx-2.6.2-8 C:\Users\<USER>\.gradle\caches\8.13\transforms\2af3bcc447ac110862396e7d0b0ec9ae\transformed\lifecycle-runtime-ktx-2.6.2\res
com.sloumache.awardsApp.app-recyclerview-1.1.0-9 C:\Users\<USER>\.gradle\caches\8.13\transforms\2db4240268c509e9b190ca1a393ca7d5\transformed\recyclerview-1.1.0\res
com.sloumache.awardsApp.app-drawerlayout-1.1.1-10 C:\Users\<USER>\.gradle\caches\8.13\transforms\34bb67911f8414f1ff69475eb4175853\transformed\drawerlayout-1.1.1\res
com.sloumache.awardsApp.app-expo.modules.filesystem-18.1.11-11 C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\res
com.sloumache.awardsApp.app-core-runtime-2.2.0-12 C:\Users\<USER>\.gradle\caches\8.13\transforms\3b4cadc5d581274a383a17f34b96bacf\transformed\core-runtime-2.2.0\res
com.sloumache.awardsApp.app-lifecycle-viewmodel-2.6.2-13 C:\Users\<USER>\.gradle\caches\8.13\transforms\44911df0b7c9625b5575bd1446144add\transformed\lifecycle-viewmodel-2.6.2\res
com.sloumache.awardsApp.app-profileinstaller-1.3.1-14 C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\res
com.sloumache.awardsApp.app-fragment-ktx-1.6.1-15 C:\Users\<USER>\.gradle\caches\8.13\transforms\506e255793c8bac2342ad449777733c1\transformed\fragment-ktx-1.6.1\res
com.sloumache.awardsApp.app-autofill-1.1.0-16 C:\Users\<USER>\.gradle\caches\8.13\transforms\50fb524026069718687c4ab652c29053\transformed\autofill-1.1.0\res
com.sloumache.awardsApp.app-lifecycle-runtime-2.6.2-17 C:\Users\<USER>\.gradle\caches\8.13\transforms\5379c5771c0cc88a961c78af9bb09339\transformed\lifecycle-runtime-2.6.2\res
com.sloumache.awardsApp.app-cardview-1.0.0-18 C:\Users\<USER>\.gradle\caches\8.13\transforms\5745f858b74ff0a2f8e17d4b26e19812\transformed\cardview-1.0.0\res
com.sloumache.awardsApp.app-emoji2-1.3.0-19 C:\Users\<USER>\.gradle\caches\8.13\transforms\5895da00ec1caed18a5ab5e37ba29377\transformed\emoji2-1.3.0\res
com.sloumache.awardsApp.app-tracing-1.2.0-20 C:\Users\<USER>\.gradle\caches\8.13\transforms\5e0f9e6bf46b1bd6aeea3c4a7ba42299\transformed\tracing-1.2.0\res
com.sloumache.awardsApp.app-media-1.0.0-21 C:\Users\<USER>\.gradle\caches\8.13\transforms\6183b82bc0c6a1834671e2c74c065f3b\transformed\media-1.0.0\res
com.sloumache.awardsApp.app-viewpager2-1.0.0-22 C:\Users\<USER>\.gradle\caches\8.13\transforms\65ba2f9fa3c9fe83e5152e0f7195116c\transformed\viewpager2-1.0.0\res
com.sloumache.awardsApp.app-savedstate-ktx-1.2.1-23 C:\Users\<USER>\.gradle\caches\8.13\transforms\6d8784d24425b9166c30f1a4f5571e15\transformed\savedstate-ktx-1.2.1\res
com.sloumache.awardsApp.app-material-1.12.0-24 C:\Users\<USER>\.gradle\caches\8.13\transforms\7becb12098085a58be85c10a694bf84d\transformed\material-1.12.0\res
com.sloumache.awardsApp.app-core-ktx-1.16.0-25 C:\Users\<USER>\.gradle\caches\8.13\transforms\891968078fca34d66479f7dc5674a685\transformed\core-ktx-1.16.0\res
com.sloumache.awardsApp.app-lifecycle-livedata-core-2.6.2-26 C:\Users\<USER>\.gradle\caches\8.13\transforms\8fbfdc943d04e82c10add9c26833a5c6\transformed\lifecycle-livedata-core-2.6.2\res
com.sloumache.awardsApp.app-drawee-3.6.0-27 C:\Users\<USER>\.gradle\caches\8.13\transforms\90554224eff49d6253bc7f46090a200f\transformed\drawee-3.6.0\res
com.sloumache.awardsApp.app-transition-1.5.0-28 C:\Users\<USER>\.gradle\caches\8.13\transforms\925e579c481c06349ca2c1bdadbdec35\transformed\transition-1.5.0\res
com.sloumache.awardsApp.app-annotation-experimental-1.4.1-29 C:\Users\<USER>\.gradle\caches\8.13\transforms\9478ca0554caeea9ae571964e0cb2ca6\transformed\annotation-experimental-1.4.1\res
com.sloumache.awardsApp.app-coordinatorlayout-1.2.0-30 C:\Users\<USER>\.gradle\caches\8.13\transforms\94f09ae075da36273dd5b0f48955e034\transformed\coordinatorlayout-1.2.0\res
com.sloumache.awardsApp.app-lifecycle-viewmodel-ktx-2.6.2-31 C:\Users\<USER>\.gradle\caches\8.13\transforms\98eec38dab2893f9dde1642e7a91ed70\transformed\lifecycle-viewmodel-ktx-2.6.2\res
com.sloumache.awardsApp.app-lifecycle-process-2.6.2-32 C:\Users\<USER>\.gradle\caches\8.13\transforms\a60d671dd3aa291ecde7f9adf86d9123\transformed\lifecycle-process-2.6.2\res
com.sloumache.awardsApp.app-core-1.16.0-33 C:\Users\<USER>\.gradle\caches\8.13\transforms\ad3fede12f460d3ded14c5028a9d47f8\transformed\core-1.16.0\res
com.sloumache.awardsApp.app-appcompat-1.7.0-34 C:\Users\<USER>\.gradle\caches\8.13\transforms\af9a9d7ace362c65c6063a55648731b1\transformed\appcompat-1.7.0\res
com.sloumache.awardsApp.app-swiperefreshlayout-1.1.0-35 C:\Users\<USER>\.gradle\caches\8.13\transforms\c3179e0f1b67cb33410fa674edb5496c\transformed\swiperefreshlayout-1.1.0\res
com.sloumache.awardsApp.app-lifecycle-viewmodel-savedstate-2.6.2-36 C:\Users\<USER>\.gradle\caches\8.13\transforms\c4b770dc8989251f37e56402d8aa1685\transformed\lifecycle-viewmodel-savedstate-2.6.2\res
com.sloumache.awardsApp.app-activity-1.8.0-37 C:\Users\<USER>\.gradle\caches\8.13\transforms\ce37a28011a1b60b4e3d132dbd8b1975\transformed\activity-1.8.0\res
com.sloumache.awardsApp.app-emoji2-views-helper-1.3.0-38 C:\Users\<USER>\.gradle\caches\8.13\transforms\ec1853d0b0976e32a4f952e316c0c604\transformed\emoji2-views-helper-1.3.0\res
com.sloumache.awardsApp.app-react-android-0.79.5-debug-39 C:\Users\<USER>\.gradle\caches\8.13\transforms\ecf58a1d5a3b5cc4faabc13e873181f2\transformed\react-android-0.79.5-debug\res
com.sloumache.awardsApp.app-lifecycle-livedata-2.6.2-40 C:\Users\<USER>\.gradle\caches\8.13\transforms\fa08d204398219a1a89b242b31fa3041\transformed\lifecycle-livedata-2.6.2\res
com.sloumache.awardsApp.app-core-viewtree-1.0.0-41 C:\Users\<USER>\.gradle\caches\8.13\transforms\fb7c56538b0ba4939b6926ce198168cc\transformed\core-viewtree-1.0.0\res
com.sloumache.awardsApp.app-pngs-42 C:\Users\<USER>\Desktop\awards\awardsApp\android\app\build\generated\res\pngs\debug
com.sloumache.awardsApp.app-resValues-43 C:\Users\<USER>\Desktop\awards\awardsApp\android\app\build\generated\res\resValues\debug
com.sloumache.awardsApp.app-packageDebugResources-44 C:\Users\<USER>\Desktop\awards\awardsApp\android\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.sloumache.awardsApp.app-packageDebugResources-45 C:\Users\<USER>\Desktop\awards\awardsApp\android\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.sloumache.awardsApp.app-debug-46 C:\Users\<USER>\Desktop\awards\awardsApp\android\app\build\intermediates\merged_res\debug\mergeDebugResources
com.sloumache.awardsApp.app-debug-47 C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\debug\res
com.sloumache.awardsApp.app-main-48 C:\Users\<USER>\Desktop\awards\awardsApp\android\app\src\main\res
com.sloumache.awardsApp.app-debug-49 C:\Users\<USER>\Desktop\awards\awardsApp\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\packaged_res\debug\packageDebugResources
com.sloumache.awardsApp.app-debug-50 C:\Users\<USER>\Desktop\awards\awardsApp\node_modules\expo-constants\android\build\intermediates\packaged_res\debug\packageDebugResources
com.sloumache.awardsApp.app-debug-51 C:\Users\<USER>\Desktop\awards\awardsApp\node_modules\expo-modules-core\android\build\intermediates\packaged_res\debug\packageDebugResources
com.sloumache.awardsApp.app-debug-52 C:\Users\<USER>\Desktop\awards\awardsApp\node_modules\expo\android\build\intermediates\packaged_res\debug\packageDebugResources
com.sloumache.awardsApp.app-debug-53 C:\Users\<USER>\Desktop\awards\awardsApp\node_modules\react-native-edge-to-edge\android\build\intermediates\packaged_res\debug\packageDebugResources
com.sloumache.awardsApp.app-debug-54 C:\Users\<USER>\Desktop\awards\awardsApp\node_modules\react-native-gesture-handler\android\build\intermediates\packaged_res\debug\packageDebugResources
com.sloumache.awardsApp.app-debug-55 C:\Users\<USER>\Desktop\awards\awardsApp\node_modules\react-native-safe-area-context\android\build\intermediates\packaged_res\debug\packageDebugResources
com.sloumache.awardsApp.app-debug-56 C:\Users\<USER>\Desktop\awards\awardsApp\node_modules\react-native-screens\android\build\intermediates\packaged_res\debug\packageDebugResources
com.sloumache.awardsApp.app-debug-57 C:\Users\<USER>\Desktop\awards\awardsApp\node_modules\react-native-vector-icons\android\build\intermediates\packaged_res\debug\packageDebugResources
